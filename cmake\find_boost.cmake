﻿include(FetchContent)
set(BOOST_DEPS_ROOT "http://pan.aqrose.com/f/9b11a50edee8443db78f/?dl=1&p=boost.1.74.0.zip")
FetchContent_Declare(
    boost
    URL ${BOOST_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(boost)

set(BOOST_VERSION_MAJOR 1)
set(BOOST_VERSION_MINOR 74)
set(BOOST_VERSION_PATCH 0)

if(NOT boost_POPULATED)
    FetchContent_Populate(boost)
    set(boost_modules thread system regex log log_setup filesystem date_time chrono atomic)
    foreach(mod ${boost_modules})
        add_library(AIDIBOOST::${mod} SHARED IMPORTED)
        set_target_properties(
            AIDIBOOST::${mod}
            PROPERTIES
            IMPORTED_LOCATION
            ${boost_SOURCE_DIR}/build/Release/x64/boost_${mod}-vc142-mt-x64-${BOOST_VERSION_MAJOR}_${BOOST_VERSION_MINOR}.dll
            IMPORTED_LOCATION_DEBUG
            ${boost_SOURCE_DIR}/build/Release/x64/boost_${mod}-vc142-mt-gd-x64-${BOOST_VERSION_MAJOR}_${BOOST_VERSION_MINOR}.dll
            IMPORTED_IMPLIB
            ${boost_SOURCE_DIR}/build/Release/x64/boost_${mod}-vc142-mt-x64-${BOOST_VERSION_MAJOR}_${BOOST_VERSION_MINOR}.lib
            IMPORTED_IMPLIB_DEBUG
            ${boost_SOURCE_DIR}/build/Debug/x64/boost_${mod}-vc142-mt-gd-x64-${BOOST_VERSION_MAJOR}_${BOOST_VERSION_MINOR}.lib
            INTERFACE_INCLUDE_DIRECTORIES
            ${boost_SOURCE_DIR}/include
            INTERFACE_COMPILE_DEFINITIONS
            BOOST_ALL_DYN_LINK
        )
        install(
            FILES
            $<TARGET_FILE:AIDIBOOST::${mod}>
            DESTINATION release
        )
    endforeach(mod)
endif()
