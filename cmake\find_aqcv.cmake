﻿include(FetchContent)
set(AQCV_DEPS_ROOT "https://pan.aqrose.com/f/bd92cd84429d480c847e/?dl=1&p=aqcv1142b2-1.1.42-beta.2-win-vc142-AMD64.zip")
FetchContent_Declare(
    aqcv
    URL ${AQCV_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(aqcv)

if(NOT aqcv_POPULATED)
    FetchContent_Populate(aqcv)
    add_library(AIDIAQCV SHARED IMPORTED)
    set_target_properties(
        AIDIAQCV
        PROPERTIES
        IMPORTED_LOCATION
        ${aqcv_SOURCE_DIR}/bin/Release/aqcv1142b2.dll
        IMPORTED_LOCATION_DEBUG
        ${aqcv_SOURCE_DIR}/bin/Debug/aqcv1142b2d.dll
        IMPORTED_IMPLIB
        ${aqcv_SOURCE_DIR}/build/Release/aqcv1142b2.lib
        IMPORTED_IMPLIB_DEBUG
        ${aqcv_SOURCE_DIR}/build/Debug/aqcv1142b2d.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${aqcv_SOURCE_DIR}/include
    )
    add_library(AIDIGEOMETRY SHARED IMPORTED)
    set_target_properties(
        AIDIGEOMETRY
        PROPERTIES
        IMPORTED_LOCATION
        ${aqcv_SOURCE_DIR}/bin/Release/geometry1033a9.dll
        IMPORTED_LOCATION_DEBUG
        ${aqcv_SOURCE_DIR}/bin/Debug/geometry1033a9d.dll
        IMPORTED_IMPLIB
        ${aqcv_SOURCE_DIR}/build/Release/geometry1033a9.lib
        IMPORTED_IMPLIB_DEBUG
        ${aqcv_SOURCE_DIR}/build/Debug/geometry1033a9d.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${aqcv_SOURCE_DIR}/include
    )
    add_library(LIBCRYPTO SHARED IMPORTED)
    set_target_properties(
        LIBCRYPTO 
        PROPERTIES
        IMPORTED_LOCATION 
        ${aqcv_SOURCE_DIR}/bin/Release/libcrypto-1_1-x64.dll
    )


    install(
        FILES
        $<TARGET_FILE:AIDIGEOMETRY>
        $<TARGET_FILE:AIDIAQCV>
        $<TARGET_FILE:LIBCRYPTO>
        DESTINATION release
    )
endif()
