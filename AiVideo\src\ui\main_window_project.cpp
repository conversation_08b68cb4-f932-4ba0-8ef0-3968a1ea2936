#include "ui/main_window.h"
#include "core/project_manager.h"
#include "utils/dialog_utils.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>
#include <QDir>
#include <QStatusBar>
#include <QSpinBox>

namespace ui {

void MainWindow::newProject() {
    // 如果当前项目有未保存的更改，提示保存
    if (!currentProjectPath.isEmpty()) {
        QMessageBox::StandardButton reply = QMessageBox::question(this, tr("新建项目"),
            tr("是否保存当前项目的更改？"),
            QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel);

        if (reply == QMessageBox::Yes) {
            saveProject();
        } else if (reply == QMessageBox::Cancel) {
            return;
        }
    }

    // 创建新项目
    auto project = core::ProjectManager::get_instance().create_project();

    // 重置UI状态
    videoPathEdit->clear();
    modelPathEdit->clear();
    inputNodeIdEdit->setText("输入");
    outputNodeIdEdit->setText("分割/pred");

    // 关闭当前视频
    if (videoProcessingCore->is_video_opened()) {
        videoProcessingCore->close_video();
    }

    // 重置当前项目路径
    currentProjectPath.clear();

    // 更新窗口标题
    setWindowTitle(tr("阿丘视频AI分析平台VAS - 未命名项目"));

    // 显示成功消息
    statusBar()->showMessage(tr("新项目已创建"), 3000);
}

void MainWindow::openProject() {
    // 如果当前项目有未保存的更改，提示保存
    if (!currentProjectPath.isEmpty()) {
        QMessageBox::StandardButton reply = QMessageBox::question(this, tr("打开项目"),
            tr("是否保存当前项目的更改？"),
            QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel);

        if (reply == QMessageBox::Yes) {
            saveProject();
        } else if (reply == QMessageBox::Cancel) {
            return;
        }
    }

    // 获取项目文件路径
    QString documentsPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QString filePath = QFileDialog::getOpenFileName(this, tr("打开项目"),
                                                  documentsPath,
                                                  tr("项目文件 (*.aivp)"));

    if (filePath.isEmpty()) {
        return;
    }

    // 打开项目
    auto project = core::ProjectManager::get_instance().open_project(filePath.toStdString());
    if (!project) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法打开项目文件：%1").arg(filePath),
            QMessageBox::Critical);
        return;
    }

    // 确保插件已加载，避免在import_to_video_processing_core中重复加载
    ensureTaskPluginsLoaded();

    // 导入项目设置到VideoProcessingCore
    if (!core::ProjectManager::get_instance().import_to_video_processing_core(project, videoProcessingCore)) {
        utils::showScrollableMessageBox(this, tr("警告"),
            tr("项目设置导入失败，部分设置可能未正确应用。"),
            QMessageBox::Warning);
    }

    // 更新UI
    QString videoPath = QString::fromStdString(project->get_video_path());
    QString modelPath = QString::fromStdString(project->get_model_path());
    videoPathEdit->setText(videoPath);
    modelPathEdit->setText(modelPath);
    inputNodeIdEdit->setText(QString::fromStdString(project->get_input_node_id()));
    outputNodeIdEdit->setText(QString::fromStdString(project->get_output_node_id()));

    // 自动加载视频
    if (!videoPath.isEmpty()) {
        std::cout << "Auto-loading video from project: " << videoPath.toStdString() << std::endl;
        loadVideo(videoPath);
    }

    // 如果模型路径存在但模型未加载，尝试加载模型
    if (!modelPath.isEmpty() && !videoProcessingCore->is_model_loaded()) {
        std::cout << "Auto-loading model from project: " << modelPath.toStdString() << std::endl;
        loadModel(modelPath);
    }

    // 如果模型已加载，启用AI处理模式
    if (videoProcessingCore->is_model_loaded()) {
        std::cout << "Model is loaded, enabling AI processing mode" << std::endl;
        enableAIProcessingMode(true);
    }

    // 更新其他设置
    lastPrompt = QString::fromStdString(project->get_prompt());
    lastScore = project->get_score_threshold();
    lastIou = project->get_iou_threshold();
    frameSkipInterval = project->get_frame_skip_interval();
    recordingOutputPath = QString::fromStdString(project->get_recording_output_path());
    maxFrameHistory = project->get_max_frame_history();

    // 更新UI控件的值
    QSpinBox* frameHistorySpinBox = findChild<QSpinBox*>("frameHistorySpinBox");
    if (frameHistorySpinBox) {
        frameHistorySpinBox->setValue(maxFrameHistory);
    }

    QSpinBox* frameSkipSpinBox = findChild<QSpinBox*>("frameSkipSpinBox");
    if (frameSkipSpinBox) {
        frameSkipSpinBox->setValue(frameSkipInterval);
    }

    // 更新用户定义的类别名称
    userDefinedClassNames.clear();
    for (const auto& className : project->get_user_defined_class_names()) {
        userDefinedClassNames.append(QString::fromStdString(className));
    }

    // 更新结果存储设置
    enableResultStorage = project->get_enable_result_storage();
    resultStorageTcpPort = project->get_result_storage_tcp_port();
    resultStorageMode = static_cast<core::VideoResultStorageServer::StorageMode>(project->get_result_storage_mode());
    resultStorageFlushInterval = project->get_result_storage_flush_interval();

    // 如果启用了结果存储，启动结果存储服务
    if (enableResultStorage) {
        videoProcessingCore->start_result_storage_server(
            "results",
            resultStorageMode,
            resultStorageTcpPort,
            resultStorageFlushInterval
        );
    }

    // 更新帧处理器状态
    updateFrameProcessorStatus();

    // 更新当前项目路径
    currentProjectPath = filePath;

    // 更新窗口标题
    // 使用QFileInfo来正确处理中文文件名
    QFileInfo fileInfo(filePath);
    QString baseName = fileInfo.baseName();
    setWindowTitle(tr("阿丘视频AI分析平台VAS - %1").arg(baseName));
    std::cout << "Project name: " << baseName.toStdString() << std::endl;

    // 添加到最近打开的项目列表
    core::ProjectManager::get_instance().add_to_recent_projects(filePath.toStdString());
    updateRecentProjectsMenu();

    // 显示成功消息
    statusBar()->showMessage(tr("项目已打开：%1").arg(filePath), 3000);
}

void MainWindow::saveProject() {
    if (currentProjectPath.isEmpty()) {
        saveProjectAs();
        return;
    }

    // 创建项目对象
    auto project = std::make_shared<core::Project>();

    // 从VideoProcessingCore导出设置
    if (!core::ProjectManager::get_instance().export_from_video_processing_core(videoProcessingCore, project)) {
        utils::showScrollableMessageBox(this, tr("警告"),
            tr("项目设置导出失败，部分设置可能未正确保存。"),
            QMessageBox::Warning);
    }

    // 设置项目名称
    project->set_name(QFileInfo(currentProjectPath).baseName().toStdString());

    // 设置其他设置
    project->set_prompt(lastPrompt.toStdString());
    project->set_score_threshold(lastScore);
    project->set_iou_threshold(lastIou);
    project->set_frame_skip_interval(frameSkipInterval);
    project->set_recording_output_path(recordingOutputPath.toStdString());
    project->set_max_frame_history(maxFrameHistory);

    // 设置用户定义的类别名称
    std::vector<std::string> classNames;
    for (const auto& className : userDefinedClassNames) {
        classNames.push_back(className.toStdString());
    }
    project->set_user_defined_class_names(classNames);

    // 设置结果存储设置
    project->set_enable_result_storage(enableResultStorage);
    project->set_result_storage_tcp_port(resultStorageTcpPort);
    project->set_result_storage_mode(static_cast<int>(resultStorageMode));
    project->set_result_storage_flush_interval(resultStorageFlushInterval);

    // 保存插件状态
    if (pluginManager) {
        // 获取所有插件
        auto plugins = pluginManager->get_all_plugins();

        // 保存启用的插件列表
        std::vector<std::string> enabledPlugins;
        for (const auto& plugin : plugins) {
            std::string pluginName = plugin->get_name();
            // 直接使用插件的is_enabled方法检查插件是否启用
            if (plugin->is_enabled()) {
                enabledPlugins.push_back(pluginName);
                std::cout << "保存启用的插件: " << pluginName << std::endl;
            }
        }

        // 设置启用的插件列表
        project->set_enabled_plugins(enabledPlugins);

        // 打印启用的插件列表，用于调试
        std::cout << "保存到工程的启用插件列表: " << std::endl;
        for (const auto& pluginName : enabledPlugins) {
            std::cout << "  " << pluginName << std::endl;
        }
    }

    // 保存项目
    if (!core::ProjectManager::get_instance().save_project(project, currentProjectPath.toStdString())) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法保存项目文件：%1").arg(currentProjectPath),
            QMessageBox::Critical);
        return;
    }

    // 显示成功消息
    statusBar()->showMessage(tr("项目已保存：%1").arg(currentProjectPath), 3000);
}

void MainWindow::saveProjectAs() {
    // 获取保存路径
    QString documentsPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QString filePath = QFileDialog::getSaveFileName(this, tr("保存项目"),
                                                  documentsPath,
                                                  tr("项目文件 (*.aivp)"));

    if (filePath.isEmpty()) {
        return;
    }

    // 确保文件扩展名为.aivp
    if (!filePath.endsWith(".aivp", Qt::CaseInsensitive)) {
        filePath += ".aivp";
    }

    // 更新当前项目路径
    currentProjectPath = filePath;

    // 保存项目
    saveProject();

    // 更新窗口标题
    // 使用QFileInfo来正确处理中文文件名
    QString baseName = QFileInfo(filePath).baseName();
    setWindowTitle(tr("阿丘视频AI分析平台VAS - %1").arg(baseName));
    std::cout << "Project name: " << baseName.toStdString() << std::endl;

    // 添加到最近打开的项目列表
    core::ProjectManager::get_instance().add_to_recent_projects(filePath.toStdString());
    updateRecentProjectsMenu();
}

void MainWindow::openRecentProject(const QString& filePath) {
    // 如果当前项目有未保存的更改，提示保存
    if (!currentProjectPath.isEmpty()) {
        QMessageBox::StandardButton reply = QMessageBox::question(this, tr("打开项目"),
            tr("是否保存当前项目的更改？"),
            QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel);

        if (reply == QMessageBox::Yes) {
            saveProject();
        } else if (reply == QMessageBox::Cancel) {
            return;
        }
    }

    // 检查文件是否存在
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("项目文件不存在：%1").arg(filePath),
            QMessageBox::Critical);

        // 从最近打开的项目列表中移除
        core::ProjectManager::get_instance().add_to_recent_projects(filePath.toStdString());
        updateRecentProjectsMenu();
        return;
    }

    // 打开项目
    auto project = core::ProjectManager::get_instance().open_project(filePath.toStdString());
    if (!project) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("无法打开项目文件：%1").arg(filePath),
            QMessageBox::Critical);
        return;
    }

    // 确保插件已加载，避免在import_to_video_processing_core中重复加载
    ensureTaskPluginsLoaded();

    // 导入项目设置到VideoProcessingCore
    if (!core::ProjectManager::get_instance().import_to_video_processing_core(project, videoProcessingCore)) {
        utils::showScrollableMessageBox(this, tr("警告"),
            tr("项目设置导入失败，部分设置可能未正确应用。"),
            QMessageBox::Warning);
    }

    // 更新UI
    QString videoPath = QString::fromStdString(project->get_video_path());
    QString modelPath = QString::fromStdString(project->get_model_path());
    videoPathEdit->setText(videoPath);
    modelPathEdit->setText(modelPath);
    inputNodeIdEdit->setText(QString::fromStdString(project->get_input_node_id()));
    outputNodeIdEdit->setText(QString::fromStdString(project->get_output_node_id()));

    // 自动加载视频
    if (!videoPath.isEmpty()) {
        std::cout << "Auto-loading video from recent project: " << videoPath.toStdString() << std::endl;
        loadVideo(videoPath);
    }

    // 如果模型路径存在但模型未加载，尝试加载模型
    if (!modelPath.isEmpty() && !videoProcessingCore->is_model_loaded()) {
        std::cout << "Auto-loading model from recent project: " << modelPath.toStdString() << std::endl;
        loadModel(modelPath);
    }

    // 如果模型已加载，启用AI处理模式
    if (videoProcessingCore->is_model_loaded()) {
        std::cout << "Model is loaded, enabling AI processing mode" << std::endl;
        enableAIProcessingMode(true);
    }

    // 更新其他设置
    lastPrompt = QString::fromStdString(project->get_prompt());
    lastScore = project->get_score_threshold();
    lastIou = project->get_iou_threshold();
    frameSkipInterval = project->get_frame_skip_interval();
    recordingOutputPath = QString::fromStdString(project->get_recording_output_path());
    maxFrameHistory = project->get_max_frame_history();

    // 更新UI控件的值
    QSpinBox* frameHistorySpinBox = findChild<QSpinBox*>("frameHistorySpinBox");
    if (frameHistorySpinBox) {
        frameHistorySpinBox->setValue(maxFrameHistory);
    }

    QSpinBox* frameSkipSpinBox = findChild<QSpinBox*>("frameSkipSpinBox");
    if (frameSkipSpinBox) {
        frameSkipSpinBox->setValue(frameSkipInterval);
    }

    // 更新用户定义的类别名称
    userDefinedClassNames.clear();
    for (const auto& className : project->get_user_defined_class_names()) {
        userDefinedClassNames.append(QString::fromStdString(className));
    }

    // 更新结果存储设置
    enableResultStorage = project->get_enable_result_storage();
    resultStorageTcpPort = project->get_result_storage_tcp_port();
    resultStorageMode = static_cast<core::VideoResultStorageServer::StorageMode>(project->get_result_storage_mode());
    resultStorageFlushInterval = project->get_result_storage_flush_interval();

    // 如果启用了结果存储，启动结果存储服务
    if (enableResultStorage) {
        videoProcessingCore->start_result_storage_server(
            "results",
            resultStorageMode,
            resultStorageTcpPort,
            resultStorageFlushInterval
        );
    }

    // 更新帧处理器状态
    updateFrameProcessorStatus();

    // 更新当前项目路径
    currentProjectPath = filePath;

    // 更新窗口标题
    // 使用QFileInfo来正确处理中文文件名
    QString baseName = QFileInfo(filePath).baseName();
    setWindowTitle(tr("阿丘视频AI分析平台VAS - %1").arg(baseName));
    std::cout << "Project name: " << baseName.toStdString() << std::endl;

    // 添加到最近打开的项目列表
    core::ProjectManager::get_instance().add_to_recent_projects(filePath.toStdString());
    updateRecentProjectsMenu();

    // 显示成功消息
    statusBar()->showMessage(tr("项目已打开：%1").arg(filePath), 3000);
}

void MainWindow::clearRecentProjects() {
    core::ProjectManager::get_instance().clear_recent_projects();
    updateRecentProjectsMenu();
}

void MainWindow::updateRecentProjectsMenu() {
    // 清空菜单
    recentProjectsMenu->clear();

    // 清空动作列表
    for (auto action : recentProjectActions) {
        delete action;
    }
    recentProjectActions.clear();

    // 获取最近打开的项目列表
    auto recentProjects = core::ProjectManager::get_instance().get_recent_projects();

    // 如果列表为空，添加一个禁用的动作
    if (recentProjects.empty()) {
        QAction* noRecentAction = new QAction(tr("无最近打开的项目"), this);
        noRecentAction->setEnabled(false);
        recentProjectsMenu->addAction(noRecentAction);
        recentProjectActions.append(noRecentAction);
    } else {
        // 添加最近打开的项目
        for (const auto& project : recentProjects) {
            QString filePath = QString::fromStdString(project);
            QFileInfo fileInfo(filePath);
            QString displayName = fileInfo.baseName();
            std::cout << "Recent project: " << displayName.toStdString() << std::endl;

            QAction* action = new QAction(displayName, this);
            action->setData(filePath);
            connect(action, &QAction::triggered, [this, filePath]() {
                openRecentProject(filePath);
            });

            recentProjectsMenu->addAction(action);
            recentProjectActions.append(action);
        }

        // 添加分隔线
        recentProjectsMenu->addSeparator();

        // 添加清除菜单项
        QAction* clearAction = new QAction(tr("清除最近打开的项目"), this);
        connect(clearAction, &QAction::triggered, this, &MainWindow::clearRecentProjects);
        recentProjectsMenu->addAction(clearAction);
        recentProjectActions.append(clearAction);
    }
}

} // namespace ui
