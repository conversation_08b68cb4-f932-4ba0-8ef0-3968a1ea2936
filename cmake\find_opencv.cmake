﻿
include(FetchContent)
set(OPENCV_DEPS_ROOT "http://pan.aqrose.com/f/228c9f7967124fc58d9f/?dl=1&p=opencv-4.7.0-win-vc16-x64.zip")

FetchContent_Declare(
  opencv
  URL ${OPENCV_DEPS_ROOT}
  UPDATE_DISCONNECTED
)

FetchContent_GetProperties(opencv)

if(NOT opencv_POPULATED)
    FetchContent_Populate(opencv)
    add_library(AIDIOPENCV SHARED IMPORTED)
	set_target_properties(
        AIDIOPENCV
		PROPERTIES
        IMPORTED_IMPLIB
        ${opencv_SOURCE_DIR}/x64/vc16/lib/opencv_world470.lib
        IMPORTED_IMPLIB_DEBUG
        ${opencv_SOURCE_DIR}/x64/vc16/lib/opencv_world470.lib
        IMPORTED_IMPLIB_RELEASE
        ${opencv_SOURCE_DIR}/x64/vc16/lib/opencv_world470.lib
		IMPORTED_LOCATION
        ${opencv_SOURCE_DIR}/x64/vc16/bin/opencv_world470.dll
		IMPORTED_LOCATION_DEBUG
        ${opencv_SOURCE_DIR}/x64/vc16/bin/opencv_world470.dll
        IMPORTED_LOCATION_RELEASE
        ${opencv_SOURCE_DIR}/x64/vc16/bin/opencv_world470.dll
		INTERFACE_INCLUDE_DIRECTORIES
        ${opencv_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDIOPENCV>
        DESTINATION release
    )
endif()
