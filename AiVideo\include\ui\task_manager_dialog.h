#pragma once

#include <QCheckBox>
#include <QDialog>
#include <QHBoxLayout>
#include <QLabel>
#include <QListWidget>
#include <QMenu>
#include <QPushButton>
#include <QVBoxLayout>

#include <memory>

#include "ai/ai_processor.h"
#include "core/project.h"

namespace ui {

/**
 * @brief 任务管理对话框
 */
class TaskManagerDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param processor AI处理器
     * @param project 项目对象
     * @param parent 父窗口
     */
    explicit TaskManagerDialog(std::shared_ptr<ai::AiProcessor> processor,
                              std::shared_ptr<core::Project> project,
                              QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~TaskManagerDialog() override;

    /**
     * @brief 刷新任务列表
     */
    void refreshTaskList();

private slots:
    /**
     * @brief 配置选中的任务
     */
    void configureSelectedTask();

    /**
     * @brief 启用/禁用选中的任务
     */
    void toggleSelectedTask();

    /**
     * @brief 处理任务列表项双击
     * @param item 列表项
     */
    void onTaskItemDoubleClicked(QListWidgetItem* item);

    /**
     * @brief 处理任务列表右键菜单
     * @param pos 位置
     */
    void onTaskListContextMenu(const QPoint& pos);

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 创建任务列表项
     * @param plugin 插件
     * @return 列表项
     */
    QListWidgetItem* createTaskItem(std::shared_ptr<ai::plugins::TaskPlugin> plugin);

    /**
     * @brief 更新任务列表项
     * @param item 列表项
     * @param plugin 插件
     */
    void updateTaskItem(QListWidgetItem* item, std::shared_ptr<ai::plugins::TaskPlugin> plugin);

    std::shared_ptr<ai::AiProcessor> processor_;  ///< AI处理器
    std::shared_ptr<core::Project> project_;      ///< 项目对象
    QVBoxLayout* mainLayout_;                     ///< 主布局
    QListWidget* taskListWidget_;                 ///< 任务列表控件
    QPushButton* configButton_;                   ///< 配置按钮
    QPushButton* toggleButton_;                   ///< 启用/禁用按钮
    QPushButton* closeButton_;                    ///< 关闭按钮
    QMenu* contextMenu_;                          ///< 右键菜单
};

} // namespace ui
