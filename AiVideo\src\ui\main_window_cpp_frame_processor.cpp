#include <QAction>
#include <QComboBox>
#include <QDialog>
#include <QDir>
#include <QFile>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QLabel>
#include <QMenu>
#include <QMenuBar>
#include <QMessageBox>
#include <QPushButton>
#include <QRadioButton>
#include <QVBoxLayout>

#include "ui/cpp_frame_processor_dialog.h"
#include "ui/main_window.h"
#include "utils/dialog_utils.h"

namespace ui {

void MainWindow::openCppFrameProcessorDialog() {
    // 检查模型是否已加载
    if (!videoProcessingCore->get_model_manager()->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    try {
        // 获取 AI 处理器的 C++ 帧处理器
        auto processor = videoProcessingCore->get_ai_processor()->get_cpp_frame_processor();
        if (!videoProcessingCore->get_ai_processor()) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("C++ 帧处理器未初始化"),
                QMessageBox::Warning);
            return;
        }

        // 初始化 C++ 帧处理器
        if (!videoProcessingCore->get_ai_processor()->initialize_cpp_frame_processor()) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("C++ 帧处理器初始化失败"),
                QMessageBox::Warning);
            return;
        }

        // 创建 C++ 帧处理器对话框
        ui::CppFrameProcessorDialog dialog(processor, this);

        // 显示对话框
        if (dialog.exec() == QDialog::Accepted) {
            // 更新帧处理器状态标签
            updateFrameProcessorStatus();
        }
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("打开 C++ 帧处理器对话框时出错：%1").arg(e.what()),
            QMessageBox::Warning);
    }
}

void MainWindow::toggleFrameProcessorType(bool useCpp) {
    // 检查模型是否已加载
    if (!videoProcessingCore->get_model_manager()->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    try {
        // 获取 AI 处理器
        auto aiProcessor = videoProcessingCore->get_ai_processor();
        if (!aiProcessor) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("AI 处理器未初始化"),
                QMessageBox::Warning);
            return;
        }

        // 设置是否使用 C++ 帧处理器
        aiProcessor->set_use_cpp_frame_processor(useCpp);

        // 如果切换到 C++ 帧处理器，确保它已初始化
        if (useCpp && !aiProcessor->initialize_cpp_frame_processor()) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("C++ 帧处理器初始化失败"),
                QMessageBox::Warning);
            return;
        }

        // 更新帧处理器状态标签
        updateFrameProcessorStatus();

        // 显示切换成功消息
        utils::showScrollableMessageBox(this, tr("成功"),
            useCpp ? tr("已切换到 C++ 帧处理器") : tr("已切换到 Python 帧处理器"),
            QMessageBox::Information);
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("切换帧处理器类型时出错：%1").arg(e.what()),
            QMessageBox::Warning);
    }
}

} // namespace ui
