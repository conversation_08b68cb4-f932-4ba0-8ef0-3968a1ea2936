#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QTableWidget>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QGridLayout>
#include <QFileDialog>
#include <QMessageBox>
#include <QSettings>
#include <QCloseEvent>
#include <QMenu>
#include <QAction>
#include <QProgressDialog>
#include <QElapsedTimer>
#include <QApplication>
#include <QThread>
#include <QDebug>

#include "streammanager.h"
#include "videostreamwidget.h"
#include "port_checker.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    bool eventFilter(QObject *watched, QEvent *event) override;

private slots:
    // 菜单动作
    void on_actionExit_triggered();
    void on_actionAbout_triggered();
    void on_actionSaveConfig_triggered();
    void on_actionLoadConfig_triggered();

    // 配置管理
    void on_addStreamButton_clicked();
    void on_removeStreamButton_clicked();
    void on_configTable_cellClicked(int row, int column);
    void on_configTable_cellChanged(int row, int column);

    // 视频控制
    void on_startAllButton_clicked();
    void on_pauseAllButton_clicked();
    void on_stopAllButton_clicked();

    // 布局控制
    void on_layoutComboBox_currentIndexChanged(int index);

    // 其他
    void updateStreamDisplay();
    void browseVideoFile();
    void browseProjectFile();

    // 单个视频流控制
    void onStartStream(int streamId);
    void onPauseStream(int streamId);
    void onStopStream(int streamId);

    // 窗口控制
    void minimizeWindow();
    void maximizeWindow();
    void closeWindow();

private:
    Ui::MainWindow *ui;
    StreamManager *streamManager;
    QTimer *updateTimer;

    // 界面元素
    QTableWidget *configTable;
    QGridLayout *videoLayout;
    QComboBox *layoutComboBox;
    QPushButton *startAllButton;
    QPushButton *pauseAllButton;
    QPushButton *stopAllButton;
    QPushButton *addStreamButton;
    QPushButton *removeStreamButton;

    // 视频流显示控件
    QList<VideoStreamWidget*> videoWidgets;

    // 无标题栏窗口拖动相关
    bool isDragging;
    QPoint dragPosition;
    QWidget *titleBar;
    QPushButton *minimizeButton;
    QPushButton *maximizeButton;
    QPushButton *closeButton;

    // 初始化函数
    void setupUi();
    void createActions();
    void createMenus();
    void initializeStreamManager();
    void updateConfigTable();
    void updateVideoWidgets();
    void saveSettings();
    void loadSettings();
    void applyLayout(int layoutType);

    // 进度对话框相关
    QProgressDialog* createProgressDialog(const QString& title, const QString& labelText);
    void processEvents();

    // 端口检查相关
    bool checkPort(int port, bool showWarning = true);
    int findAvailablePort(int startPort = 8888);
};

#endif // MAINWINDOW_H
