/*
 * Blue Theme Style Sheet for AiVideo
 * Flat design with blue as the primary color
 */

/* Define color variables */
/* Primary Colors */
* {
    --primary-color: #1976D2;           /* Primary blue */
    --primary-light: #42A5F5;           /* Lighter blue for hover states */
    --primary-dark: #0D47A1;            /* Darker blue for pressed states */
    --primary-text: #FFFFFF;            /* Text on primary color */

    /* Secondary Colors */
    --secondary-color: #E3F2FD;         /* Very light blue for backgrounds */
    --secondary-light: #F5F9FF;         /* Even lighter blue for hover states */
    --secondary-dark: #BBDEFB;          /* Slightly darker blue for borders */

    /* Neutral Colors */
    --background-color: #F5F5F5;        /* Light gray for main background */
    --surface-color: #FFFFFF;           /* White for cards and surfaces */
    --text-primary: #212121;            /* Dark gray for primary text */
    --text-secondary: #757575;          /* Medium gray for secondary text */
    --border-color: #E0E0E0;            /* Light gray for borders */

    /* Accent Colors */
    --success-color: #4CAF50;           /* Green for success states */
    --warning-color: #FFC107;           /* Yellow for warning states */
    --error-color: #F44336;             /* Red for error states */
    --info-color: #2196F3;              /* Blue for info states */
}

/* Global Application Style */
QWidget {
    background-color: #F5F5F5;
    color: #212121;
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    font-size: 12px;
}

/* Animation Settings */
QPushButton, QRadioButton, QCheckBox, QComboBox, QLineEdit, QSlider::handle {
    transition: background-color 0.2s, border-color 0.2s;
}

/* Main Window */
QMainWindow {
    background-color: #F5F5F5;
    padding: 0;
    margin: 0;
}

QMainWindow::separator {
    background-color: #E0E0E0;
    width: 1px;
    height: 1px;
}

/* Central Widget */
#centralWidget {
    background-color: #F5F5F5;
    border: none;
    margin: 0;
    padding: 0;
}

/* Panel Widgets */
#leftPanel, #rightPanel {
    background-color: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #E0E0E0;
    padding: 5px;
}

#leftPanel {
    margin-right: 5px;
}

#rightPanel {
    margin-left: 5px;
}

/* Add shadow effect to panels */
#leftPanel, #rightPanel, #videoPlayerGroup, #processingGroup, #aiSettingsGroup {
    border: 1px solid #BBDEFB;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Menu Bar */
QMenuBar {
    background-color: #1976D2;
    color: white;
    border: none;
    padding: 2px;
    font-weight: bold;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
    margin: 1px 2px;
}

QMenuBar::item:selected {
    background-color: #42A5F5;
}

QMenuBar::item:pressed {
    background-color: #0D47A1;
}

/* Menu */
QMenu {
    background-color: white;
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    padding: 4px 0px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

QMenu::item {
    padding: 8px 25px 8px 20px;
    border: none;
}

QMenu::item:selected {
    background-color: #E3F2FD;
    color: #1976D2;
}

QMenu::item:disabled {
    color: #BDBDBD;
}

QMenu::separator {
    height: 1px;
    background-color: #BBDEFB;
    margin: 4px 10px;
}

QMenu::indicator {
    width: 16px;
    height: 16px;
    margin-left: 5px;
}

/* Status Bar */
QStatusBar {
    background-color: #1976D2;
    color: white;
    border-top: 1px solid #0D47A1;
    min-height: 24px;
    padding: 2px 10px;
    font-weight: bold;
}

QStatusBar::item {
    border: none;
}

QStatusBar QLabel {
    color: white;
}

/* Group Box */
QGroupBox {
    border: 1px solid #BBDEFB;
    border-radius: 6px;
    margin-top: 16px;
    background-color: white;
    padding-top: 20px;
    padding-bottom: 10px;
    padding-left: 10px;
    padding-right: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 8px;
    color: #1976D2;
    font-weight: bold;
    font-size: 13px;
}

/* Push Button */
QPushButton {
    background-color: #1976D2;
    color: white;
    border: 2px solid #1565C0;  /* 添加深蓝色边线 */
    border-radius: 4px;
    padding: 6px 16px;
    min-width: 80px;
    outline: none;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #1E88E5;
    border-color: #1976D2;
}

QPushButton:pressed {
    background-color: #1565C0;
    border-color: #0D47A1;
}

QPushButton:disabled {
    background-color: #BDBDBD;
    border-color: #9E9E9E;
    color: #FFFFFF;
}

/* Secondary Button Style */
QPushButton.secondary {
    background-color: #FFFFFF;
    color: #1976D2;
    border: 2px solid #1976D2;
}

QPushButton.secondary:hover {
    background-color: #E3F2FD;
    border-color: #1E88E5;
}

QPushButton.secondary:pressed {
    background-color: #BBDEFB;
    border-color: #1565C0;
}

/* 危险操作按钮样式 */
QPushButton.danger {
    background-color: #F44336;
    border-color: #D32F2F;
    color: white;
}

QPushButton.danger:hover {
    background-color: #EF5350;
    border-color: #F44336;
}

QPushButton.danger:pressed {
    background-color: #D32F2F;
    border-color: #C62828;
}

/* 特殊按钮样式（如初始化模型、测试AI等） */
#testAiButton {
    background-color: #2196F3;
    border: 2px solid #1976D2;
    color: white;
    padding: 8px 16px;
    font-weight: bold;
}

#testAiButton:hover {
    background-color: #42A5F5;
    border-color: #2196F3;
}

#testAiButton:pressed {
    background-color: #1976D2;
    border-color: #1565C0;
}

/* Line Edit */
QLineEdit {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    padding: 5px;
    background-color: white;
    selection-background-color: #42A5F5;
    selection-color: white;
}

QLineEdit:focus {
    border: 1px solid #1976D2;
}

QLineEdit:disabled {
    background-color: #F5F5F5;
    color: #9E9E9E;
}

/* Node ID Edit */
#nodeIdEdit {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    padding: 8px;
    background-color: white;
    font-family: "Consolas", "Courier New", monospace;
    font-size: 12px;
}

#nodeIdEdit:focus {
    border: 2px solid #1976D2;
    background-color: #F5F9FF;
}

/* Combo Box */
QComboBox {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    padding: 5px;
    background-color: white;
    min-width: 6em;
}

QComboBox:hover {
    border: 1px solid #1976D2;
}

QComboBox:on {
    border: 1px solid #1976D2;
    background-color: #E3F2FD;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QComboBox::down-arrow {
    image: url(:/rc/icons/down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #BBDEFB;
    border-radius: 0px;
    background-color: white;
    selection-background-color: #E3F2FD;
    selection-color: #1976D2;
}

/* Spin Box */
QSpinBox, QDoubleSpinBox {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    padding: 5px;
    background-color: white;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border: 1px solid #1976D2;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    subcontrol-origin: border;
    subcontrol-position: top right;
    width: 16px;
    border-left: 1px solid #BBDEFB;
    border-bottom: 1px solid #BBDEFB;
    border-top-right-radius: 4px;
    background-color: #F5F5F5;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    subcontrol-origin: border;
    subcontrol-position: bottom right;
    width: 16px;
    border-left: 1px solid #BBDEFB;
    border-bottom-right-radius: 4px;
    background-color: #F5F5F5;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #E3F2FD;
}

QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed,
QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
    background-color: #BBDEFB;
}

/* Check Box */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 1px solid #BBDEFB;
    border-radius: 3px;
    background-color: white;
}

QCheckBox::indicator:unchecked:hover {
    border: 1px solid #1976D2;
}

QCheckBox::indicator:checked {
    background-color: #1976D2;
    border: 1px solid #1976D2;
    image: url(:/rc/icons/check.png);
}

/* Radio Button */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border: 1px solid #BBDEFB;
    border-radius: 9px;
    background-color: white;
}

QRadioButton::indicator:unchecked:hover {
    border: 1px solid #1976D2;
}

QRadioButton::indicator:checked {
    background-color: white;
    border: 1px solid #1976D2;
}

QRadioButton::indicator:checked {
    image: none;
    background-color: white;
    border: 1px solid #1976D2;
}

QRadioButton::indicator:checked::after {
    content: "";
    display: block;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: #1976D2;
    position: absolute;
    top: 4px;
    left: 4px;
}

/* Processing Radio Buttons */
#processingRadio {
    font-weight: bold;
    color: #1976D2;
    padding: 5px;
    border-radius: 4px;
}

#processingRadio:hover {
    background-color: #E3F2FD;
}

#processingRadio:checked {
    background-color: #E3F2FD;
}

/* Slider */
QSlider::groove:horizontal {
    border: none;
    height: 6px;
    background-color: #E0E0E0;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #1976D2;
    border: none;
    width: 16px;
    height: 16px;
    margin: -5px 0;
    border-radius: 8px;
}

QSlider::handle:horizontal:hover {
    background-color: #42A5F5;
}

QSlider::handle:horizontal:pressed {
    background-color: #0D47A1;
}

QSlider::add-page:horizontal {
    background-color: #E0E0E0;
    border-radius: 3px;
}

QSlider::sub-page:horizontal {
    background-color: #1976D2;
    border-radius: 3px;
}

/* Video Slider */
#videoSlider {
    height: 24px;
}

#videoSlider::groove:horizontal {
    border: none;
    height: 8px;
    background-color: #E0E0E0;
    border-radius: 4px;
}

#videoSlider::handle:horizontal {
    background-color: #1976D2;
    border: 2px solid white;
    width: 20px;
    height: 20px;
    margin: -6px 0;
    border-radius: 10px;
}

#videoSlider::handle:horizontal:hover {
    background-color: #42A5F5;
}

#videoSlider::handle:horizontal:pressed {
    background-color: #0D47A1;
}

#videoSlider::sub-page:horizontal {
    background-color: #1976D2;
    border-radius: 4px;
}

/* Progress Bar */
QProgressBar {
    border: none;
    border-radius: 3px;
    background-color: #E0E0E0;
    text-align: center;
    color: #212121;
    height: 20px;
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: #1976D2;
    border-radius: 3px;
}

/* Video Info Label */
#videoInfoLabel {
    color: #757575;
    font-size: 11px;
    padding: 5px;
    background-color: #F5F9FF;
    border-radius: 4px;
    border: 1px solid #BBDEFB;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    background-color: white;
    top: -1px;
}

QTabBar::tab {
    background-color: #F5F5F5;
    border: 1px solid #BBDEFB;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 6px 12px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: white;
    border: 1px solid #1976D2;
    border-bottom: none;
}

QTabBar::tab:hover:!selected {
    background-color: #E3F2FD;
}

/* Scroll Bar */
QScrollBar:vertical {
    border: none;
    background-color: #F5F5F5;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #BDBDBD;
    border-radius: 5px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9E9E9E;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #F5F5F5;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #BDBDBD;
    border-radius: 5px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9E9E9E;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* List Widget */
QListWidget {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    background-color: white;
    outline: none;
}

QListWidget::item {
    padding: 5px;
    border-bottom: 1px solid #E0E0E0;
}

QListWidget::item:selected {
    background-color: #E3F2FD;
    color: #1976D2;
}

QListWidget::item:hover:!selected {
    background-color: #F5F9FF;
}

/* Tree Widget */
QTreeWidget {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    background-color: white;
    outline: none;
}

QTreeWidget::item {
    padding: 5px;
    border-bottom: 1px solid #E0E0E0;
}

QTreeWidget::item:selected {
    background-color: #E3F2FD;
    color: #1976D2;
}

QTreeWidget::item:hover:!selected {
    background-color: #F5F9FF;
}

/* Table Widget */
QTableWidget {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    background-color: white;
    gridline-color: #E0E0E0;
    outline: none;
}

QTableWidget::item {
    padding: 5px;
}

QTableWidget::item:selected {
    background-color: #E3F2FD;
    color: #1976D2;
}

QHeaderView::section {
    background-color: #F5F5F5;
    padding: 5px;
    border: 1px solid #E0E0E0;
    border-left: none;
    border-top: none;
}

QHeaderView::section:first {
    border-left: 1px solid #E0E0E0;
}

/* Tool Bar */
QToolBar {
    background-color: #F5F5F5;
    border-right: 1px solid #E0E0E0;
    spacing: 3px;
    padding: 3px;
}

/* 工具栏分组标签样式 */
QToolBar QLabel {
    color: #1976D2;
    font-weight: bold;
    font-size: 11px;
    padding: 4px 0;
    margin: 4px 0;
    background-color: #E3F2FD;
    border-radius: 2px;
    width: 100%;
}

/* 工具栏分隔线样式 */
QToolBar::separator {
    background-color: #BBDEFB;
    height: 1px;
    margin: 6px 4px;
}

QToolButton {
    border: 1px solid #BBDEFB;
    border-radius: 4px;
    padding: 4px;
    margin: 2px;
    background-color: transparent;
}

QToolButton:hover {
    background-color: #E3F2FD;
    border-color: #90CAF9;
}

QToolButton:pressed {
    background-color: #BBDEFB;
    border-color: #64B5F6;
}

/* 垂直工具栏特定样式 */
QToolBar[orientation="vertical"] QToolButton {
    min-width: 60px;
    min-height: 50px;
    margin: 2px 1px;
}

QToolBar[orientation="vertical"] QToolButton QLabel {
    margin-top: 4px;
}

QToolButton[popupMode="1"] { /* only for MenuButtonPopup */
    padding-right: 20px; /* make way for the popup button */
}

QToolButton::menu-button {
    border: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    width: 16px;
}

QToolButton::menu-button:hover {
    background-color: #42A5F5;
}

QToolButton::menu-button:pressed {
    background-color: #0D47A1;
}

/* 视频控制按钮组样式 */
#playButton, #stopButton, #fullscreenButton {
    min-width: 90px;
    min-height: 36px;
    padding: 6px 16px;
    margin: 0 8px;
    border: 2px solid #1976D2;
    border-radius: 6px;
    background-color: #2196F3;
    color: white;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
}

#playButton:hover, #stopButton:hover, #fullscreenButton:hover {
    background-color: #42A5F5;
    border-color: #2196F3;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#playButton:pressed, #stopButton:pressed, #fullscreenButton:pressed {
    background-color: #1976D2;
    border-color: #1565C0;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 播放按钮特殊样式 */
#playButton {
    background-color: #4CAF50;  /* 绿色 */
    border-color: #388E3C;
}

#playButton:hover {
    background-color: #66BB6A;
    border-color: #4CAF50;
}

#playButton:pressed {
    background-color: #388E3C;
    border-color: #2E7D32;
}

/* 停止按钮特殊样式 */
#stopButton {
    background-color: #F44336;  /* 红色 */
    border-color: #D32F2F;
}

#stopButton:hover {
    background-color: #EF5350;
    border-color: #F44336;
}

#stopButton:pressed {
    background-color: #D32F2F;
    border-color: #C62828;
}

/* 全屏按钮特殊样式 */
#fullscreenButton {
    background-color: #FF9800;  /* 橙色 */
    border-color: #F57C00;
}

#fullscreenButton:hover {
    background-color: #FFA726;
    border-color: #FF9800;
}

#fullscreenButton:pressed {
    background-color: #F57C00;
    border-color: #EF6C00;
}

/* 控制按钮图标样式 */
#playButton, #stopButton, #fullscreenButton {
    qproperty-iconSize: QSize(20, 20);  /* 增大图标尺寸 */
}

/* 控制按钮布局容器样式 */
#controlLayout {
    background-color: #F5F5F5;
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
}

/* 对话框按钮样式 */
QDialog QPushButton {
    min-width: 80px;
    min-height: 30px;
    border: 2px solid #1976D2;
}

/* Dialog */
QDialog {
    background-color: #F5F5F5;
}

/* Message Box */
QMessageBox {
    background-color: #F5F5F5;
}

QMessageBox QPushButton {
    min-width: 80px;
    min-height: 24px;
}

/* Label */
QLabel {
    color: #212121;
}

/* Title Container */
#titleContainer {
    background-color: white;  /* 改为白色背景 */
    border-radius: 6px;
    padding: 5px 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 窗口控制按钮样式 */
#minimizeButton, #maximizeButton, #closeButton {
    background-color: transparent;
    border: none;
    border-radius: 15px;
    min-width: 30px;
    min-height: 30px;
    padding: 0;
    margin: 0 2px;
}

#minimizeButton {
    qproperty-icon: url(:/rc/icons/minimize_icon.png);
}

#minimizeButton:hover {
    background-color: #E3F2FD;
}

#minimizeButton:pressed {
    background-color: #BBDEFB;
}

#maximizeButton {
    qproperty-icon: url(:/rc/icons/maximize_icon.png);
}

#maximizeButton:hover {
    background-color: #E3F2FD;
}

#maximizeButton:pressed {
    background-color: #BBDEFB;
}

#closeButton {
    qproperty-icon: url(:/rc/icons/close_icon.png);
}

#closeButton:hover {
    background-color: #FFCDD2;
}

#closeButton:pressed {
    background-color: #EF9A9A;
}

/* Title Label */
QLabel[title="true"] {
    font-size: 16px;
    font-weight: bold;
    padding: 0;
    margin: 0;
    color: #1976D2;  /* 使用主题蓝色 */
}

/* Version Label */
QLabel[version="true"] {
    color: #E3F2FD;
    font-size: 12px;
}

/* Hint Label */
QLabel[class="hint"] {
    color: #757575;
    font-size: 8pt;
    font-style: italic;
}

/* Video Display */
QLabel#videoDisplay {
    background-color: #1E1E1E;  /* 更深的背景色 */
    color: #FFFFFF;
    border-radius: 8px;
    border: 2px solid #1976D2;
    padding: 10px;
    font-size: 14px;
    font-weight: bold;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
}

/* Video Player Group */
#videoPlayerGroup {
    padding: 15px;
    background-color: #F5F9FF;  /* 浅蓝色背景 */
    border: 2px solid #1976D2;  /* 加粗的边框 */
    border-radius: 10px;
    margin: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);  /* 添加阴影效果 */
}

#videoPlayerGroup > QLabel[title="true"] {
    color: #1976D2;
    font-size: 13px;
    font-weight: bold;
    padding: 5px 0;
}

/* 视频控制面板样式 */
#controlPanel {
    background-color: #FFFFFF;
    border: 1px solid #BBDEFB;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
}

/* 视频进度条样式 */
#videoSlider {
    height: 24px;  /* 增加高度 */
    margin: 5px 0;
}

#videoSlider::groove:horizontal {
    border: 1px solid #BBDEFB;
    height: 8px;
    background: #E3F2FD;
    border-radius: 4px;
}

#videoSlider::handle:horizontal {
    background: #1976D2;
    border: 2px solid #FFFFFF;
    width: 16px;
    height: 16px;
    margin: -5px 0;
    border-radius: 8px;
}

#videoSlider::sub-page:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #42A5F5, stop:1 #1976D2);
    border-radius: 4px;
}

/* Video Info Label */
QLabel#videoInfoLabel {
    color: #1976D2;
    font-size: 11px;
    font-weight: bold;
    padding: 5px 10px;
    background-color: #E3F2FD;
    border-radius: 4px;
    border: 1px solid #BBDEFB;
    margin: 5px 0;
}

/* Control Panel */
#controlPanel {
    background-color: #F5F9FF;
    border-radius: 6px;
    border: 1px solid #BBDEFB;
    padding: 10px;
}

/* Processing Group */
#processingGroup, #aiSettingsGroup, #modeGroupBox {
    background-color: white;
    border: 1px solid #BBDEFB;
    border-radius: 6px;
}

#processingGroup::title, #aiSettingsGroup::title, #modeGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 8px;
    color: #1976D2;
    font-weight: bold;
    font-size: 13px;
}

/* Video Slider */
#videoSlider {
    height: 20px;
}

/* Frame */
QFrame[frameShape="4"] {  /* HLine */
    background-color: #BBDEFB;
    max-height: 1px;
}

QFrame[frameShape="5"] {  /* VLine */
    background-color: #BBDEFB;
    max-width: 1px;
}

/* Tooltip */
QToolTip {
    background-color: #1976D2;
    color: white;
    border: none;
    padding: 8px 12px;
    opacity: 230;
    border-radius: 4px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}








