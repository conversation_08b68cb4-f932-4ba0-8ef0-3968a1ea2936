﻿#pragma once

#include <QDialog>
#include <QDoubleSpinBox>
#include <QLineEdit>
#include <QString>

namespace ui {

/**
 * @brief 万物检测大模型参数设置对话框
 */
class ModelSettingsDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param defaultPrompt 默认提示词
     * @param defaultScore 默认置信度阈值
     * @param defaultIou 默认IOU阈值
     * @param parent 父窗口
     */
    ModelSettingsDialog(const QString& defaultPrompt = "fire",
                       double defaultScore = 0.15,
                       int defaultIou = 70,
                       QWidget* parent = nullptr);

    /**
     * @brief 获取提示词
     * @return 提示词
     */
    QString getPromptValue() const;

    /**
     * @brief 获取置信度阈值
     * @return 置信度阈值
     */
    double getScoreValue() const;

    /**
     * @brief 获取IOU阈值
     * @return IOU阈值
     */
    int getIouValue() const;

private:
    QLineEdit* promptEdit;        ///< 提示词输入框
    QDoubleSpinBox* scoreSpinBox; ///< 置信度阈值输入框
    QSpinBox* iouSpinBox;         ///< IOU阈值输入框
};

} // namespace ui
