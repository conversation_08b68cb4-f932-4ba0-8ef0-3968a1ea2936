﻿include(FetchContent)
set(OSG_DEPS_ROOT "http://pan.aqrose.com/f/313ca6be50764a248899/?dl=1&p=OpenSceneGraph.zip")
FetchContent_Declare(
    osg
    URL ${OSG_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(osg)

if(NOT osg_POPULATED)
    FetchContent_Populate(osg)
    add_library(OpenThreads SHARED IMPORTED)
    set_target_properties(
        OpenThreads
        PROPERTIES
        IMPORTED_LOCATION
        ${osg_SOURCE_DIR}/build/Release/x64/ot21-OpenThreads.dll
        IMPORTED_LOCATION_DEBUG
        ${osg_SOURCE_DIR}/build/Release/x64/ot21-OpenThreads.dll
        IMPORTED_IMPLIB
        ${osg_SOURCE_DIR}/build/Release/x64/OpenThreads.lib
        IMPORTED_IMPLIB_DEBUG
        ${osg_SOURCE_DIR}/build/Release/x64/OpenThreads.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${osg_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:OpenThreads>
        DESTINATION release
    )
    set(osg_modules osg osgAnimation osgDB osgFX osgGA osgManipulator osgParticle osgPresentation osgShadow osgSim osgTerrain osgText osgUI osgUtil osgViewer osgVolume osgWidget)
    foreach(mod ${osg_modules})
        add_library(${mod} SHARED IMPORTED)
        set_target_properties(
            ${mod}
            PROPERTIES
            IMPORTED_LOCATION
            ${osg_SOURCE_DIR}/build/Release/x64/osg202-${mod}.dll
            IMPORTED_LOCATION_DEBUG
            ${osg_SOURCE_DIR}/build/Release/x64/osg202-${mod}.dll
            IMPORTED_IMPLIB
            ${osg_SOURCE_DIR}/build/Release/x64/${mod}.lib
            IMPORTED_IMPLIB_DEBUG
            ${osg_SOURCE_DIR}/build/Release/x64/${mod}.lib
            INTERFACE_INCLUDE_DIRECTORIES
            ${osg_SOURCE_DIR}/include
        )
        install(
            FILES
            $<TARGET_FILE:${mod}>
            DESTINATION release
        )
    endforeach(mod)
endif()

