#include "ui/result_viewer_dialog.h"

#include <QDateTime>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QScrollBar>
#include <QTextStream>
#include <json/json.h>
#include "core/protocols/result_protocol.h"

namespace ui {

ResultViewerDialog::ResultViewerDialog(QWidget* parent)
    : QDialog(parent),
      current_display_mode_(FULL_JSON),
      max_displayed_results_(100),
      result_count_(0),
      result_storage_server_(nullptr),
      refresh_timer_(nullptr) {
  // 设置窗口属性
  setWindowTitle(tr("实时处理结果查看器"));
  resize(800, 600);

  // 初始化UI组件
  init_ui();

  // 创建自动滚动定时器
  auto_scroll_timer_ = new QTimer(this);
  connect(auto_scroll_timer_, &QTimer::timeout, this,
          &ResultViewerDialog::auto_scroll_to_bottom);
  auto_scroll_timer_->start(500);  // 每500毫秒滚动一次

  // 创建刷新定时器
  refresh_timer_ = new QTimer(this);
  connect(refresh_timer_, &QTimer::timeout, this,
          &ResultViewerDialog::load_results_from_server);
  // 初始不启动定时器，等待用户选择自动刷新
}

ResultViewerDialog::~ResultViewerDialog() {
  if (auto_scroll_timer_) {
    auto_scroll_timer_->stop();
  }
  if (refresh_timer_) {
    refresh_timer_->stop();
  }
}

void ResultViewerDialog::init_ui() {
  main_layout_ = new QVBoxLayout(this);

  // 创建显示模式选择
  QHBoxLayout* mode_layout = new QHBoxLayout();
  QLabel* mode_label = new QLabel(tr("显示模式:"), this);
  display_mode_combo_ = new QComboBox(this);
  display_mode_combo_->addItem(tr("完整JSON"));
  display_mode_combo_->addItem(tr("关键信息"));
  connect(display_mode_combo_, QOverload<int>::of(&QComboBox::currentIndexChanged),
          this, &ResultViewerDialog::on_display_mode_changed);

  mode_layout->addWidget(mode_label);
  mode_layout->addWidget(display_mode_combo_);
  mode_layout->addStretch();

  // 创建自动滚动复选框
  auto_scroll_checkbox_ = new QCheckBox(tr("自动滚动"), this);
  auto_scroll_checkbox_->setChecked(false);
  mode_layout->addWidget(auto_scroll_checkbox_);

  // 创建自动刷新复选框
  auto_refresh_checkbox_ = new QCheckBox(tr("自动刷新"), this);
  auto_refresh_checkbox_->setChecked(false);
  connect(auto_refresh_checkbox_, &QCheckBox::toggled, this, [this](bool checked) {
    if (checked && refresh_timer_) {
      refresh_timer_->start(2000);  // 每2秒刷新一次
    } else if (refresh_timer_) {
      refresh_timer_->stop();
    }
  });
  mode_layout->addWidget(auto_refresh_checkbox_);

  main_layout_->addLayout(mode_layout);

  // 创建结果文本编辑器
  result_text_edit_ = new QTextEdit(this);
  result_text_edit_->setReadOnly(true);
  result_text_edit_->setLineWrapMode(QTextEdit::NoWrap);
  result_text_edit_->setFont(QFont("Consolas", 10));
  main_layout_->addWidget(result_text_edit_);

  // 创建按钮布局
  QHBoxLayout* button_layout = new QHBoxLayout();

  // 创建状态标签
  status_label_ = new QLabel(tr("已显示结果: 0"), this);
  button_layout->addWidget(status_label_);

  // 创建服务状态标签
  tcp_status_label_ = new QLabel(tr("服务: 未启动"), this);
  tcp_status_label_->setStyleSheet("color: gray;");
  button_layout->addWidget(tcp_status_label_);

  button_layout->addStretch();

  // 创建清除按钮
  clear_button_ = new QPushButton(tr("清除"), this);
  connect(clear_button_, &QPushButton::clicked, this, &ResultViewerDialog::clear_content);
  button_layout->addWidget(clear_button_);

  // 创建保存按钮
  save_button_ = new QPushButton(tr("保存"), this);
  connect(save_button_, &QPushButton::clicked, this, &ResultViewerDialog::save_to_file);
  button_layout->addWidget(save_button_);

  // 创建加载按钮
  load_button_ = new QPushButton(tr("加载结果"), this);
  connect(load_button_, &QPushButton::clicked, this, &ResultViewerDialog::load_results_from_server);
  button_layout->addWidget(load_button_);

  main_layout_->addLayout(button_layout);

  setLayout(main_layout_);
}

void ResultViewerDialog::update_result(const ai::FrameResult& result) {
  // 将结果转换为JSON
  Json::Value json_result = result.to_json();

  // 根据显示模式选择显示内容
  QString display_text;
  if (current_display_mode_ == FULL_JSON) {
    // 使用 StreamWriterBuilder 替代 writeString
    Json::StreamWriterBuilder writer;
    writer.settings_["indentation"] = "  ";  // 设置缩进

    // 使用 stringstream 来获取格式化的JSON字符串
    std::ostringstream oss;
    std::unique_ptr<Json::StreamWriter> stream_writer(writer.newStreamWriter());
    stream_writer->write(json_result, &oss);
    std::string json_str = oss.str();

    display_text = format_json(json_str);
  } else {
    display_text = extract_key_info(result);
  }

  // 添加时间戳
  QDateTime current_time = QDateTime::currentDateTime();
  QString time_str = current_time.toString("yyyy-MM-dd hh:mm:ss.zzz");

  // 添加到文本编辑器
  result_text_edit_->append("--- " + time_str + " ---");
  result_text_edit_->append(display_text);
  result_text_edit_->append("");

  // 更新结果计数
  result_count_++;
  status_label_->setText(tr("已显示结果: %1").arg(result_count_));

  // 如果超过最大显示数量，清除旧的内容
  if (result_count_ > max_displayed_results_) {
    // 获取当前文本
    QString current_text = result_text_edit_->toPlainText();

    // 找到第三个分隔符的位置（删除最早的一条记录）
    int pos = current_text.indexOf("---", 0);
    if (pos >= 0) {
      pos = current_text.indexOf("---", pos + 3);
      if (pos >= 0) {
        // 删除最早的记录
        result_text_edit_->setPlainText(current_text.mid(pos));
        result_count_--;
      }
    }
  }

  // 如果启用了自动滚动，滚动到底部
  if (auto_scroll_checkbox_->isChecked()) {
    auto_scroll_to_bottom();
  }
}

void ResultViewerDialog::auto_scroll_to_bottom() {
  if (auto_scroll_checkbox_->isChecked()) {
    QScrollBar* scrollbar = result_text_edit_->verticalScrollBar();
    scrollbar->setValue(scrollbar->maximum());
  }
}

void ResultViewerDialog::clear_content() {
  result_text_edit_->clear();
  result_count_ = 0;
  status_label_->setText(tr("已显示结果: 0"));
}

void ResultViewerDialog::save_to_file() {
  QString file_path = QFileDialog::getSaveFileName(
      this,
      tr("保存结果"),
      QDir::currentPath() + "/result_" +
          QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".txt",
      tr("文本文件 (*.txt);;所有文件 (*)")
  );

  if (file_path.isEmpty()) {
    return;
  }

  QFile file(file_path);
  if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
    QMessageBox::warning(
        this,
        tr("保存失败"),
        tr("无法打开文件进行写入: %1").arg(file_path));
    return;
  }

  QTextStream out(&file);
  out << result_text_edit_->toPlainText();

  file.close();

  QMessageBox::information(
      this,
      tr("保存成功"),
      tr("结果已保存到: %1").arg(file_path));
}

void ResultViewerDialog::on_display_mode_changed(int index) {
  current_display_mode_ = static_cast<DisplayMode>(index);

  // 清除当前显示内容，以便使用新的显示模式
  clear_content();
}

void ResultViewerDialog::update_tcp_status(bool running, int port, int clientCount,
                                     core::protocols::ProtocolType protocol_type) {
  if (running) {
    QString protocolStr;
    switch (protocol_type) {
      case core::protocols::ProtocolType::TCP:
        protocolStr = tr("TCP");
        break;
      case core::protocols::ProtocolType::MODBUS:
        protocolStr = tr("Modbus");
        break;
      case core::protocols::ProtocolType::MQTT:
        protocolStr = tr("MQTT");
        break;
      case core::protocols::ProtocolType::CUSTOM:
        protocolStr = tr("自定义");
        break;
      default:
        protocolStr = tr("未知");
        break;
    }

    tcp_status_label_->setText(tr("服务: 运行中 (%1, 端口: %2, 客户端: %3)")
                              .arg(protocolStr).arg(port).arg(clientCount));
    tcp_status_label_->setStyleSheet("color: green;");
  } else {
    tcp_status_label_->setText(tr("服务: 未启动"));
    tcp_status_label_->setStyleSheet("color: gray;");
  }
}

QString ResultViewerDialog::format_json(const std::string& json_str) {
  return QString::fromStdString(json_str);
}

QString ResultViewerDialog::extract_key_info(const ai::FrameResult& result) {
  QString info;

  // 添加基本信息
  info += tr("帧ID: %1\n").arg(result.frame_id);
  info += tr("任务类型: %1\n").arg(QString::fromStdString(result.task_type));
  info += tr("总计数: %1\n").arg(result.total_count);

  // 添加类别计数
  info += tr("类别计数:\n");
  for (const auto& [class_name, count] : result.class_counts) {
    info += tr("  %1: %2\n").arg(QString::fromStdString(class_name)).arg(count);
  }

  // 添加检测结果
  info += tr("检测到 %1 个目标\n").arg(result.detection_tracks.size());

  return info;
}

void ResultViewerDialog::set_result_storage_server(std::shared_ptr<core::VideoResultStorageServer> server) {
  result_storage_server_ = server;

  // 更新加载按钮和自动刷新复选框的状态
  bool has_server = (server != nullptr);
  load_button_->setEnabled(has_server);
  auto_refresh_checkbox_->setEnabled(has_server);

  // 更新服务状态
  if (has_server) {
    update_tcp_status(server->is_running(), server->get_port(), server->get_client_count(),
                     server->get_protocol_type());
  } else {
    update_tcp_status(false, 0, 0);
  }
}

void ResultViewerDialog::enable_auto_refresh(bool enable) {
  if (auto_refresh_checkbox_) {
    auto_refresh_checkbox_->setChecked(enable);
  }
}

void ResultViewerDialog::load_results_from_server() {
  if (!result_storage_server_) {
    return;
  }

  // 获取最近的结果文件内容
  std::vector<std::string> results = result_storage_server_->get_result_file_content(max_displayed_results_);

  if (results.empty()) {
    // 如果没有结果，尝试获取最近的结果
    std::string last_result = result_storage_server_->get_last_result();
    if (!last_result.empty()) {
      results.push_back(last_result);
    }
  }

  if (results.empty()) {
    // 仍然没有结果，显示提示信息
    if (result_count_ == 0) { // 只有当当前没有显示结果时才显示提示
      result_text_edit_->setText(tr("没有可用的结果。请等待新的处理结果生成。"));
    }
    return;
  }

  // 清除当前显示内容
  clear_content();

  // 添加新的结果
  QDateTime current_time = QDateTime::currentDateTime();
  QString time_str = current_time.toString("yyyy-MM-dd hh:mm:ss.zzz");

  result_text_edit_->append(tr("从存储服务器加载了 %1 条结果").arg(results.size()));
  result_text_edit_->append("--- " + time_str + " ---");
  result_text_edit_->append("");

  // 添加每一条结果
  for (const auto& json_str : results) {
    // 解析JSON
    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;
    Json::CharReader* reader = builder.newCharReader();
    bool parsingSuccessful = reader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errors);
    delete reader;

    if (parsingSuccessful) {
      // 根据显示模式选择显示内容
      QString display_text;
      if (current_display_mode_ == FULL_JSON) {
        display_text = format_json(json_str);
      } else {
        // 尝试将JSON转换为FrameResult对象
        try {
          ai::FrameResult result;
          result.from_json(root);
          display_text = extract_key_info(result);
        } catch (const std::exception& e) {
          // 如果转换失败，显示JSON
          display_text = format_json(json_str);
        }
      }

      result_text_edit_->append(display_text);
      result_text_edit_->append("");
      result_count_++;
    }
  }

  // 更新状态标签
  status_label_->setText(tr("已显示结果: %1").arg(result_count_));

  // 更新服务状态
  update_tcp_status(
      result_storage_server_->is_running(),
      result_storage_server_->get_port(),
      result_storage_server_->get_client_count(),
      result_storage_server_->get_protocol_type()
  );

  // 如果启用了自动滚动，滚动到底部
  if (auto_scroll_checkbox_->isChecked()) {
    auto_scroll_to_bottom();
  }
}

} // namespace ui

