# AiVideoVS - 多路视频流处理系统

AiVideoVS是一个基于AiVideoCore库开发的多路视频流处理系统，提供了视频和项目的绑定管理、视频处理控制和多视频流显示功能。

## 功能特点

1. 视频和项目的绑定管理
   - 可以为每个视频流指定不同的视频文件和项目文件
   - 支持添加和移除视频流
   - 支持保存和加载配置

2. 视频处理控制
   - 支持启动、暂停和停止所有视频流
   - 支持单独控制每个视频流

3. 多视频流显示
   - 支持多种布局方式（单个、2x1、2x2、3x2、3x3）
   - 实时显示处理结果
   - 显示视频流信息

## 编译

在项目根目录下执行以下命令：

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

## 使用方法

```bash
./bin/AiVideoVS
```

## 界面说明

1. 视频流配置区域
   - 显示所有配置的视频流
   - 可以添加和移除视频流
   - 可以设置视频路径和项目路径

2. 控制区域
   - 布局选择：选择视频显示布局
   - 全部启动：启动所有视频流
   - 全部暂停：暂停所有视频流
   - 全部停止：停止所有视频流

3. 视频显示区域
   - 显示所有视频流的处理结果
   - 每个视频流有独立的控制按钮

## 注意事项

1. 需要替换代码中的许可证ID和服务器地址为有效值
2. 项目文件必须是有效的.aivp文件
3. 视频文件可以是本地文件或RTSP流
4. 处理多路视频流会消耗较多系统资源，请确保系统有足够的计算能力
