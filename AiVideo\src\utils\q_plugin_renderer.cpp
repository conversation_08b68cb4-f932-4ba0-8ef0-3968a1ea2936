#include "utils/q_plugin_renderer.h"

#include <QPainter>
#include <QImage>
#include <QDebug>
#include <QGraphicsPolygonItem>
#include <QPolygonF>
#include <QPointF>
#include <QBrush>
#include <QPen>
#include <QObject>
#include <QGraphicsDropShadowEffect>
#include <QGraphicsEffect>

namespace utils {

QPluginRenderer::QPluginRenderer() {
}

QPluginRenderer::~QPluginRenderer() {
}

bool QPluginRenderer::render(cv::Mat& frame, const Json::Value& render_info) {
    if (frame.empty() || !render_info.isObject()) {
        return false;
    }

    // 检查是否应该渲染
    if (render_info.isMember("should_render") && !render_info["should_render"].asBool()) {
        return false;
    }

    // 创建场景并渲染
    QGraphicsScene* scene = create_scene(frame, render_info);
    if (!scene) {
        return false;
    }

    // 将场景渲染到图像
    render_scene_to_frame(scene, frame);

    // 清理资源
    delete scene;

    return true;
}

bool QPluginRenderer::render_frame_result(cv::Mat& frame, const ai::FrameResult& result) {
    if (frame.empty() || result.ext_info.empty()) {
        return false;
    }

    // 解析ext_info中的JSON字符串
    Json::Value ext_info_json;
    Json::CharReaderBuilder builder;
    std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
    std::string errors;

    bool parsing_successful = reader->parse(
        result.ext_info.c_str(),
        result.ext_info.c_str() + result.ext_info.length(),
        &ext_info_json,
        &errors
    );

    if (parsing_successful && ext_info_json.isMember("render_info")) {
        // 渲染图像
        return render(frame, ext_info_json["render_info"]);
    }

    return false;
}

bool QPluginRenderer::render_to_view(QGraphicsView* view, const cv::Mat& frame, const Json::Value& render_info) {
    if (!view || frame.empty() || !render_info.isObject()) {
        return false;
    }

    // 检查是否应该渲染
    if (render_info.isMember("should_render") && !render_info["should_render"].asBool()) {
        return false;
    }

    // 创建场景并渲染
    QGraphicsScene* scene = create_scene(frame, render_info);
    if (!scene) {
        return false;
    }

    // 设置场景到视图
    view->setScene(scene);
    view->setSceneRect(scene->sceneRect());
    view->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);

    // 注意：这里不删除scene，因为它已经被view接管
    // view会在下次setScene或自身销毁时自动删除scene

    return true;
}

bool QPluginRenderer::render_frame_result_to_view(QGraphicsView* view, const cv::Mat& frame, const ai::FrameResult& result) {
    if (!view || frame.empty() || result.ext_info.empty()) {
        return false;
    }

    // 解析ext_info中的JSON字符串
    Json::Value ext_info_json;
    Json::CharReaderBuilder builder;
    std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
    std::string errors;

    bool parsing_successful = reader->parse(
        result.ext_info.c_str(),
        result.ext_info.c_str() + result.ext_info.length(),
        &ext_info_json,
        &errors
    );

    if (parsing_successful && ext_info_json.isMember("render_info")) {
        // 渲染到视图
        return render_to_view(view, frame, ext_info_json["render_info"]);
    }

    return false;
}

bool QPluginRenderer::render_graphics_to_view(QGraphicsView* view, const QSize& frame_size, const Json::Value& render_info) {
    if (!view || !render_info.isObject()) {
        return false;
    }

    // 检查是否应该渲染
    if (render_info.isMember("should_render") && !render_info["should_render"].asBool()) {
        return false;
    }

    // 获取当前场景
    QGraphicsScene* scene = view->scene();
    if (!scene) {
        // 如果没有场景，创建一个新的场景
        scene = new QGraphicsScene();
        view->setScene(scene);
        view->setSceneRect(0, 0, frame_size.width(), frame_size.height());
    }

    // 渲染跟踪目标
    if (render_info.isMember("tracks") && render_info["tracks"].isArray()) {
        render_tracks(scene, render_info["tracks"], frame_size);
    }

    // 渲染自定义元素
    if (render_info.isMember("custom_elements") && render_info["custom_elements"].isArray()) {
        render_custom_elements(scene, render_info["custom_elements"], frame_size);
    }

    // 渲染状态信息
    if (render_info.isMember("status") && render_info["status"].isObject()) {
        render_status(scene, render_info["status"], frame_size);
    }

    // 渲染帧信息
    if (render_info.isMember("frame_info") && render_info["frame_info"].isObject()) {
        render_frame_info(scene, render_info["frame_info"], frame_size);
    }

    // 渲染已完成步骤
    if (render_info.isMember("completed_steps") && render_info["completed_steps"].isArray()) {
        render_completed_steps(scene, render_info["completed_steps"], frame_size);
    }

    return true;
}

bool QPluginRenderer::render_graphics_frame_result_to_view(QGraphicsView* view, const QSize& frame_size, const ai::FrameResult& result) {
    if (!view || result.ext_info.empty()) {
        return false;
    }

    // 解析ext_info中的JSON字符串
    Json::Value ext_info_json;
    Json::CharReaderBuilder builder;
    std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
    std::string errors;

    bool parsing_successful = reader->parse(
        result.ext_info.c_str(),
        result.ext_info.c_str() + result.ext_info.length(),
        &ext_info_json,
        &errors
    );

    if (parsing_successful && ext_info_json.isMember("render_info")) {
        // 渲染到视图
        return render_graphics_to_view(view, frame_size, ext_info_json["render_info"]);
    }

    return false;
}

void QPluginRenderer::clear_view(QGraphicsView* view) {
    if (!view) {
        return;
    }

    // 获取当前场景
    QGraphicsScene* current_scene = view->scene();

    // 创建新场景
    QGraphicsScene* new_scene = new QGraphicsScene();

    // 设置新场景
    view->setScene(new_scene);

    // 删除旧场景
    if (current_scene) {
        delete current_scene;
    }
}



QGraphicsScene* QPluginRenderer::create_scene(const cv::Mat& frame, const Json::Value& render_info) {
    if (frame.empty() || !render_info.isObject()) {
        return nullptr;
    }

    // 创建场景
    QGraphicsScene* scene = new QGraphicsScene();

    // 设置场景大小
    QSize frame_size(frame.cols, frame.rows);
    scene->setSceneRect(0, 0, frame_size.width(), frame_size.height());

    // 将OpenCV图像转换为QImage并添加到场景
    QImage image = mat_to_qimage(frame);
    if (image.isNull()) {
        delete scene;
        return nullptr;
    }

    QGraphicsPixmapItem* background = scene->addPixmap(QPixmap::fromImage(image));
    background->setZValue(0); // 设置为背景层

    // 渲染跟踪目标
    if (render_info.isMember("tracks") && render_info["tracks"].isArray()) {
        render_tracks(scene, render_info["tracks"], frame_size);
    }

    // 渲染自定义元素
    if (render_info.isMember("custom_elements") && render_info["custom_elements"].isArray()) {
        render_custom_elements(scene, render_info["custom_elements"], frame_size);
    }

    // 渲染状态信息
    if (render_info.isMember("status") && render_info["status"].isObject()) {
        render_status(scene, render_info["status"], frame_size);
    }

    // 渲染帧信息
    if (render_info.isMember("frame_info") && render_info["frame_info"].isObject()) {
        render_frame_info(scene, render_info["frame_info"], frame_size);
    }

    // 渲染已完成步骤
    if (render_info.isMember("completed_steps") && render_info["completed_steps"].isArray()) {
        render_completed_steps(scene, render_info["completed_steps"], frame_size);
    }

    return scene;
}



void QPluginRenderer::render_scene_to_frame(QGraphicsScene* scene, cv::Mat& frame) {
    if (!scene || frame.empty()) {
        return;
    }

    // 创建QImage用于渲染
    QImage image(frame.cols, frame.rows, QImage::Format_RGB888);
    image.fill(Qt::transparent);

    // 创建QPainter并渲染场景
    QPainter painter(&image);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setRenderHint(QPainter::TextAntialiasing);
    painter.setRenderHint(QPainter::SmoothPixmapTransform);
    scene->render(&painter);
    painter.end();

    // 将QImage转换回OpenCV图像
    cv::Mat rendered(image.height(), image.width(), CV_8UC3, image.bits(), image.bytesPerLine());
    cv::cvtColor(rendered, frame, cv::COLOR_RGB2BGR);
}

void QPluginRenderer::render_tracks(QGraphicsScene* scene, const Json::Value& tracks, const QSize& frame_size) {
    for (const auto& track : tracks) {
        if (!track.isObject() || !track.isMember("bbox") || !track["bbox"].isArray() || track["bbox"].size() != 4) {
            continue;
        }

        // 获取边界框
        int x = track["bbox"][0].asInt();
        int y = track["bbox"][1].asInt();
        int width = track["bbox"][2].asInt();
        int height = track["bbox"][3].asInt();

        // 获取类别和ID
        QString class_name = track.isMember("class") ? QString::fromStdString(track["class"].asString()) : "unknown";
        int track_id = track.isMember("track_id") ? track["track_id"].asInt() : -1;
        float score = track.isMember("score") ? track["score"].asFloat() : 1.0f;

        // 创建矩形
        QGraphicsRectItem* rect = scene->addRect(x, y, width, height);

        // 设置矩形样式
        QPen pen(Qt::green);
        pen.setWidth(2);
        rect->setPen(pen);
        rect->setZValue(1);

        // 创建标签
        QString label = QString("%1 %2").arg(class_name).arg(track_id >= 0 ? QString::number(track_id) : "");
        if (track.isMember("score")) {
            label += QString(" %1").arg(score, 0, 'f', 2);
        }

        // 先创建文本项以获取其边界
        QGraphicsTextItem* tempText = new QGraphicsTextItem(label);
        tempText->setFont(get_default_font(12));
        QRectF textRect = tempText->boundingRect();
        delete tempText;

        // 添加半透明背景
        QGraphicsRectItem* text_bg = scene->addRect(textRect.adjusted(-5, -2, 5, 2).translated(x, y - 20));
        text_bg->setBrush(QColor(0, 0, 0, transparency_)); // 增加不透明度
        text_bg->setPen(QPen(Qt::white, 1)); // 添加白色边框
        text_bg->setZValue(5); // 确保背景在其他元素之上

        // 创建文本项并放在背景上方
        QGraphicsTextItem* text = create_text_item(label, x, y - 20, Qt::green);
        scene->addItem(text);
        text->setZValue(6); // 确保文本在背景之上
    }
}

void QPluginRenderer::render_custom_elements(QGraphicsScene* scene, const Json::Value& custom_elements, const QSize& frame_size) {
    for (const auto& element : custom_elements) {
        if (!element.isObject() || !element.isMember("type")) {
            continue;
        }

        std::string type = element["type"].asString();

        // 处理线条
        if (type == "line" && element.isMember("points") && element["points"].isArray() && element["points"].size() == 2) {
            const auto& points = element["points"];
            if (points[0].isArray() && points[0].size() == 2 && points[1].isArray() && points[1].size() == 2) {
                int x1 = points[0][0].asInt();
                int y1 = points[0][1].asInt();
                int x2 = points[1][0].asInt();
                int y2 = points[1][1].asInt();

                QColor color = element.isMember("color") ? bgr_to_qcolor(element["color"]) : Qt::yellow;
                int thickness = element.isMember("thickness") ? element["thickness"].asInt() : 2;

                QGraphicsLineItem* line = scene->addLine(x1, y1, x2, y2);
                QPen pen(color);
                pen.setWidth(thickness);
                line->setPen(pen);
                line->setZValue(1);
            }
        }
        // 处理文本
        else if (type == "text" && element.isMember("text") && element.isMember("position")) {
            QString text = QString::fromStdString(element["text"].asString());
            const auto& position = element["position"];
            if (position.isArray() && position.size() == 2) {
                int x = position[0].asInt();
                int y = position[1].asInt();

                QColor color = element.isMember("color") ? bgr_to_qcolor(element["color"]) : Qt::green;
                float font_scale = element.isMember("font_scale") ? element["font_scale"].asFloat() : 1.0f;
                int thickness = element.isMember("thickness") ? element["thickness"].asInt() : 1;

                int font_size = static_cast<int>(12 * font_scale);

                // 先创建文本项以获取其边界
                QGraphicsTextItem* tempText = new QGraphicsTextItem(text);
                tempText->setFont(get_default_font(font_size));
                QRectF textRect = tempText->boundingRect();
                delete tempText;

                // 添加半透明背景
                QGraphicsRectItem* text_bg = scene->addRect(textRect.adjusted(-5, -2, 5, 2).translated(x, y));
                text_bg->setBrush(QColor(0, 0, 0, transparency_)); // 增加不透明度
                text_bg->setPen(QPen(Qt::white, 1)); // 添加白色边框
                text_bg->setZValue(5); // 确保背景在其他元素之上

                // 创建文本项并放在背景上方
                QGraphicsTextItem* text_item = create_text_item(text, x, y, color, font_size);
                scene->addItem(text_item);
                text_item->setZValue(6); // 确保文本在背景之上
            }
        }
        // 可以添加更多类型的自定义元素...
    }
}

void QPluginRenderer::render_status(QGraphicsScene* scene, const Json::Value& status, const QSize& frame_size) {
    // 创建状态面板
    int panel_width = 300;
    int panel_height = 70;
    // int panel_x = frame_size.width() - panel_width - 10;
    // int panel_y = 10;

    int panel_x = 10;
    int panel_y = frame_size.height() / 6;

    QGraphicsRectItem* panel = nullptr;

    int text_x = panel_x + 10;
    int text_y = panel_y + 10;
    int line_height = 20;

    // 渲染状态文本
    if (status.isMember("status_text")) {
        QString status_text = QString::fromStdString(status["status_text"].asString());

        QGraphicsTextItem* tempText = new QGraphicsTextItem(status_text);
        tempText->setFont(get_default_font(12));
        QRectF textRect = tempText->boundingRect();
        delete tempText;

        QColor text_color = status.isMember("is_error") && status["is_error"].asBool() ? Qt::red : Qt::white;
        QGraphicsTextItem* text = create_text_item(status_text, text_x, text_y, text_color);

        panel_width = textRect.width() + 20;

        // 如果panel不是空
        if (panel != nullptr) {
            // 释放panel
            delete panel;
        }
        
        panel = scene->addRect(panel_x, panel_y, panel_width, panel_height);
        panel->setBrush(QColor(0, 0, 0, transparency_));
        panel->setPen(QPen(Qt::white));
        panel->setZValue(3);

        scene->addItem(text);
        text->setZValue(10); // 确保文本在最上层
        text_y += line_height;
    }

    // 渲染当前步骤
    if (status.isMember("current_step") && !status["current_step"].asString().empty()) {
        QString step = QString::fromStdString(status["current_step"].asString());
        QString progress = status.isMember("step_progress") ?
                          QString::fromStdString(status["step_progress"].asString()) : "";

        QString step_text = QObject::tr("当前步骤: ") + step;
        if (!progress.isEmpty()) {
            step_text += " (" + progress + ")";
        }

        QGraphicsTextItem* text = create_text_item(step_text, text_x, text_y, Qt::white);
        scene->addItem(text);
        text->setZValue(10); // 确保文本在最上层
        text_y += line_height;
    }

    // 渲染类别信息
    if (status.isMember("class_info") && !status["class_info"].asString().empty()) {
        QString class_info = QString::fromStdString(status["class_info"].asString());
        QGraphicsTextItem* text = create_text_item(QObject::tr("类别: ") + class_info, text_x, text_y, Qt::white);
        scene->addItem(text);
        text->setZValue(10); // 确保文本在最上层
        text_y += line_height;
    }

    // 渲染调试信息
    if (status.isMember("debug_info") && !status["debug_info"].asString().empty()) {
        QString debug_info = QString::fromStdString(status["debug_info"].asString());
        QGraphicsTextItem* text = create_text_item(QObject::tr("调试: ") + debug_info, text_x, text_y, Qt::yellow);
        scene->addItem(text);
        text->setZValue(10); // 确保文本在最上层
        text_y += line_height;
    }
}

void QPluginRenderer::render_frame_info(QGraphicsScene* scene, const Json::Value& frame_info, const QSize& frame_size) {
    int text_x = 10;
    int text_y = 10;
    int line_height = 30;

    // 渲染帧计数
    if (frame_info.isMember("frame_count")) {
        QString frame_count = QString::number(frame_info["frame_count"].asInt());
        // 先创建文本项以获取其边界
        QGraphicsTextItem* tempText = new QGraphicsTextItem(QObject::tr("帧: ") + frame_count);
        tempText->setFont(get_default_font(12));
        QRectF textRect = tempText->boundingRect();
        delete tempText;

        // 添加半透明背景
        QGraphicsRectItem* text_bg = scene->addRect(textRect.adjusted(-5, -2, 5, 2).translated(text_x, text_y));
        text_bg->setBrush(QColor(0, 0, 0, transparency_)); // 增加不透明度
        text_bg->setPen(QPen(Qt::white, 1)); // 添加白色边框
        text_bg->setZValue(5); // 确保背景在其他元素之上

        // 创建文本项并放在背景上方
        QGraphicsTextItem* text = create_text_item(QObject::tr("帧: ") + frame_count, text_x, text_y, Qt::white);
        scene->addItem(text);
        text->setZValue(6); // 确保文本在背景之上

        text_y += line_height;
    }

    // 渲染对象计数
    if (frame_info.isMember("object_count")) {
        QString object_count = QString::number(frame_info["object_count"].asInt());
        // 先创建文本项以获取其边界
        QGraphicsTextItem* tempText = new QGraphicsTextItem(QObject::tr("对象数: ") + object_count);
        tempText->setFont(get_default_font(12));
        QRectF textRect = tempText->boundingRect();
        delete tempText;

        // 添加半透明背景
        QGraphicsRectItem* text_bg = scene->addRect(textRect.adjusted(-5, -2, 5, 2).translated(text_x, text_y));
        text_bg->setBrush(QColor(0, 0, 0, transparency_)); // 增加不透明度
        text_bg->setPen(QPen(Qt::white, 1)); // 添加白色边框
        text_bg->setZValue(5); // 确保背景在其他元素之上

        // 创建文本项并放在背景上方
        QGraphicsTextItem* text = create_text_item(QObject::tr("对象数: ") + object_count, text_x, text_y, Qt::white);
        scene->addItem(text);
        text->setZValue(6); // 确保文本在背景之上

        text_y += line_height;
    }

    // 渲染类别文本
    if (frame_info.isMember("category_text") && !frame_info["category_text"].asString().empty()) {
        QString category_text = QString::fromStdString(frame_info["category_text"].asString());
        // 先创建文本项以获取其边界
        QGraphicsTextItem* tempText = new QGraphicsTextItem(QObject::tr("类别: ") + category_text);
        tempText->setFont(get_default_font(12));
        QRectF textRect = tempText->boundingRect();
        delete tempText;

        // 添加半透明背景
        QGraphicsRectItem* text_bg = scene->addRect(textRect.adjusted(-5, -2, 5, 2).translated(text_x, text_y));
        text_bg->setBrush(QColor(0, 0, 0, transparency_)); // 增加不透明度
        text_bg->setPen(QPen(Qt::white, 1)); // 添加白色边框
        text_bg->setZValue(5); // 确保背景在其他元素之上

        // 创建文本项并放在背景上方
        QGraphicsTextItem* text = create_text_item(QObject::tr("类别: ") + category_text, text_x, text_y, Qt::white);
        scene->addItem(text);
        text->setZValue(6); // 确保文本在背景之上

        text_y += line_height;
    }
}

void QPluginRenderer::render_completed_steps(QGraphicsScene* scene, const Json::Value& completed_steps, const QSize& frame_size) {
    if (completed_steps.empty()) {
        return;
    }

    // 创建已完成步骤面板
    int panel_width = 200;
    int panel_height = 60 + completed_steps.size() * 20;
    int panel_x = 10;
    int panel_y = frame_size.height() - panel_height - 20;

    QGraphicsRectItem* panel = scene->addRect(panel_x, panel_y, panel_width, panel_height);
    panel->setBrush(QColor(0, 0, 0, transparency_));
    panel->setPen(QPen(Qt::white));
    panel->setZValue(3);

    // 添加标题
    QGraphicsTextItem* title = create_text_item(QObject::tr("已完成步骤:"), panel_x + 10, panel_y + 10, Qt::white);
    scene->addItem(title);
    title->setZValue(10); // 确保文本在最上层

    // 添加步骤列表
    int text_y = panel_y + 30;
    for (const auto& step : completed_steps) {
        if (step.isObject() && step.isMember("step_name")) {
            QString step_name = QString::fromStdString(step["step_name"].asString());
            QGraphicsTextItem* text = create_text_item("✓ " + step_name, panel_x + 10, text_y, Qt::green);
            scene->addItem(text);
            text->setZValue(10); // 确保文本在最上层
            text_y += 20;
        }
    }
}

QColor QPluginRenderer::bgr_to_qcolor(const Json::Value& bgr) {
    if (bgr.isArray() && bgr.size() >= 3) {
        int b = bgr[0].asInt();
        int g = bgr[1].asInt();
        int r = bgr[2].asInt();
        return QColor(r, g, b);
    }
    return Qt::white;
}

QFont QPluginRenderer::get_default_font(int size) {
    QFont font("Microsoft YaHei", size);
    return font;
}

QGraphicsTextItem* QPluginRenderer::create_text_item(const QString& text, qreal x, qreal y,
                                                   const QColor& color, int font_size) {
    QGraphicsTextItem* text_item = new QGraphicsTextItem(text);

    // 设置字体
    QFont font = get_default_font(font_size);
    font.setBold(true); // 使文字加粗，更容易看清
    text_item->setFont(font);

    // 设置文本颜色
    text_item->setDefaultTextColor(color);

    // 设置位置
    text_item->setPos(x, y);

    // 确保文本可见
    text_item->setZValue(10); // 使文本显示在最上层

    // 添加文本轮廓以增强可见性
    QGraphicsDropShadowEffect* shadow = new QGraphicsDropShadowEffect();
    shadow->setColor(QColor(0, 0, 0, 160));
    shadow->setBlurRadius(2);
    shadow->setOffset(1, 1);
    text_item->setGraphicsEffect(shadow);

    return text_item;
}

} // namespace utils
