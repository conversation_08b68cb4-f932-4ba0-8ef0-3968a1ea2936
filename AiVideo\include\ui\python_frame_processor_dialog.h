#pragma once

#include <QCheckBox>
#include <QDialog>
#include <QLineEdit>
#include <QPushButton>
#include <QTableWidget>
#include <QTextEdit>

#include <memory>

#include "ai/python_frame_processor.h"

namespace ui {

/**
 * @brief Python帧处理器配置对话框
 */
class PythonFrameProcessorDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param processor Python帧处理器
     * @param parent 父窗口
     */
    explicit PythonFrameProcessorDialog(std::shared_ptr<ai::PythonFrameProcessor> processor, QWidget* parent = nullptr);

private slots:
    /**
     * @brief 浏览脚本文件
     */
    void browseScript();

    /**
     * @brief 重新加载脚本
     */
    void reloadScript();

    /**
     * @brief 添加参数
     */
    void addParam();

    /**
     * @brief 删除参数
     */
    void deleteParam();

    /**
     * @brief 应用参数
     */
    void applyParams();

    /**
     * @brief 启用/禁用处理器
     * @param enabled 是否启用
     */
    void enableProcessor(bool enabled);

    /**
     * @brief 打开脚本编辑器
     */
    void openScriptEditor();

private:
    /**
     * @brief 更新参数表格
     */
    void updateParamsTable();

    /**
     * @brief 更新状态信息
     */
    void updateStatus();

    std::shared_ptr<ai::PythonFrameProcessor> processor_;  ///< Python帧处理器

    QLineEdit* scriptPathEdit_;     ///< 脚本路径编辑框
    QPushButton* browseButton_;     ///< 浏览按钮
    QPushButton* reloadButton_;     ///< 重新加载按钮
    QPushButton* editButton_;       ///< 编辑脚本按钮
    QCheckBox* enableCheckBox_;     ///< 启用复选框
    QTableWidget* paramsTable_;     ///< 参数表格
    QPushButton* addParamButton_;   ///< 添加参数按钮
    QPushButton* deleteParamButton_; ///< 删除参数按钮
    QPushButton* applyButton_;      ///< 应用按钮
    QTextEdit* statusText_;         ///< 状态文本
};

} // namespace ui
