/*
 * AiVideo 全局样式表
 * 高科技黑色主题风格
 */

/* 全局样式 - 设置默认字体 */
* {
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    color: #dfe6e9;
    background-color: #15202b;
}

/* 主窗口样式 */
QMainWindow {
    border: 1px solid #38444d;
    background: #15202b;
}

/* 菜单栏样式 */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #15202b,
                               stop:1 #192734);
    color: #dfe6e9;
    border-bottom: 1px solid #38444d;
    padding: 4px;
}

QMenuBar::item {
    background: transparent;
    padding: 6px 10px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background: rgba(0, 168, 255, 0.2);
}

QMenuBar::item:pressed {
    background: rgba(0, 168, 255, 0.3);
}

/* 头部容器样式 */
QWidget#headerContainer {
    background-color: transparent;
}

/* 菜单样式 */
QMenu {
    background-color: #192734;
    border: 1px solid #38444d;
    border-radius: 6px;
    padding: 5px 0px;
}

QMenu::item {
    padding: 8px 25px 8px 20px;
}

QMenu::item:selected {
    background: rgba(0, 168, 255, 0.2);
}

QMenu::separator {
    height: 1px;
    background-color: #38444d;
    margin: 5px 0px;
}

/* 按钮样式 */
QPushButton {
    background-color: #2980b9;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 80px;
    outline: none;
    font-size: 9pt;
}

QPushButton:hover {
    background-color: #3498db;
}

QPushButton:pressed {
    background-color: #1a5c8a;
}

QPushButton:disabled {
    background-color: #192734;
    color: #38444d;
}

/* 次要按钮样式 */
QPushButton.secondary {
    background-color: #192734;
    color: #00a8ff;
    border: 1px solid #00a8ff;
}

QPushButton.secondary:hover {
    background-color: rgba(0, 168, 255, 0.1);
}

QPushButton.secondary:pressed {
    background-color: rgba(0, 168, 255, 0.2);
}

/* 危险按钮样式 */
QPushButton.danger {
    background-color: #e74c3c;
    border: none;
}

QPushButton.danger:hover {
    background-color: #c0392b;
}

/* 组框样式 */
QGroupBox {
    border: 1px solid #38444d;
    border-radius: 8px;
    margin-top: 15px;
    font-weight: bold;
    padding: 10px;
    background: rgba(25, 39, 52, 0.8);
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 0px 5px;
    color: #0abde3;
}

/* 标签样式 */
QLabel {
    color: #dfe6e9;
}

/* 标题栏样式 */
QWidget#titleBar, QWidget#titleContainer {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #192734,
                               stop:1 #15202b);
    min-height: 36px;
    max-height: 36px;
}

#titleBar QLabel, #titleContainer QLabel {
    color: #dfe6e9;
    font-size: 10pt;
    font-weight: bold;
    background-color: transparent;
}

/* 自定义菜单栏容器 */
QWidget#customMenuBar {
    background-color: transparent;
}

/* 隐藏默认菜单栏 */
QMainWindow > QMenuBar:not(#menuBar) {
    max-height: 0px;
    min-height: 0px;
    color: transparent;
    background-color: transparent;
    border: none;
}

/* 窗口控制按钮样式 */
#minimizeButton, #maximizeButton, #closeButton {
    background: transparent;
    border: none;
    border-radius: 0;
    min-width: 40px;
    max-width: 40px;
    min-height: 28px;
    max-height: 28px;
    padding: 0;
    margin: 0;
    font-family: "Segoe UI", "Microsoft YaHei";
    font-size: 10pt;
    color: #dfe6e9;
}

#minimizeButton:hover, #maximizeButton:hover {
    background: rgba(255, 255, 255, 0.1);
}

#minimizeButton:pressed, #maximizeButton:pressed {
    background: rgba(255, 255, 255, 0.2);
}

#closeButton:hover {
    background: rgba(255, 0, 0, 0.7);
}

#closeButton:pressed {
    background: rgb(255, 0, 0);
}

/* 视频控制按钮样式 */
#playButton, #stopButton, #fullscreenButton {
    min-width: 80px;
    min-height: 32px;
    padding: 6px 12px;
    margin: 0 5px;
    border: none;
    border-radius: 4px;
    font-size: 10pt;
    font-weight: bold;
}

#playButton {
    background-color: #27ae60;
}

#playButton:hover {
    background-color: #2ecc71;
}

#playButton:pressed {
    background-color: #16a085;
}

#stopButton {
    background-color: #e74c3c;
}

#stopButton:hover {
    background-color: #c0392b;
}

#stopButton:pressed {
    background-color: #c0392b;
}

#fullscreenButton {
    background-color: #f39c12;
}

#fullscreenButton:hover {
    background-color: #f1c40f;
}

#fullscreenButton:pressed {
    background-color: #d35400;
}

/* 输入框样式 */
QLineEdit {
    border: 1px solid #38444d;
    border-radius: 4px;
    padding: 5px;
    background-color: #192734;
    selection-background-color: rgba(0, 168, 255, 0.3);
    selection-color: #dfe6e9;
    color: #dfe6e9;
}

QLineEdit:focus {
    border: 1px solid #00a8ff;
}

QLineEdit:disabled {
    background-color: #15202b;
    color: #38444d;
}

/* 下拉框样式 */
QComboBox {
    border: 1px solid #38444d;
    border-radius: 6px;
    padding: 5px 10px;
    background-color: #192734;
    selection-background-color: rgba(0, 168, 255, 0.2);
    selection-color: #dfe6e9;
    min-width: 6em;
}

QComboBox:hover {
    border: 1px solid #00a8ff;
}

QComboBox:on {
    border: 1px solid #00a8ff;
    background-color: rgba(0, 168, 255, 0.1);
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QComboBox QAbstractItemView {
    border: 1px solid #38444d;
    border-radius: 6px;
    background-color: #192734;
    selection-background-color: rgba(0, 168, 255, 0.2);
}

/* 复选框样式 */
QCheckBox {
    spacing: 8px;
    color: #dfe6e9;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 1px solid #38444d;
    border-radius: 3px;
    background-color: #192734;
}

QCheckBox::indicator:unchecked:hover {
    border: 1px solid #00a8ff;
}

QCheckBox::indicator:checked {
    background-color: #00a8ff;
    border: 1px solid #00a8ff;
}

/* 单选按钮样式 */
QRadioButton {
    spacing: 8px;
    color: #dfe6e9;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border: 1px solid #38444d;
    border-radius: 9px;
    background-color: #192734;
}

QRadioButton::indicator:unchecked:hover {
    border: 1px solid #00a8ff;
}

QRadioButton::indicator:checked {
    background-color: #192734;
    border: 1px solid #00a8ff;
}

QRadioButton::indicator:checked::before {
    content: "";
    display: block;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: #00a8ff;
    margin: 4px;
}

/* 滑块样式 */
QSlider::groove:horizontal {
    border: none;
    height: 6px;
    background-color: #38444d;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #00a8ff;
    border: none;
    width: 16px;
    height: 16px;
    margin: -5px 0;
    border-radius: 8px;
}

QSlider::handle:horizontal:hover {
    background-color: #0abde3;
}

QSlider::sub-page:horizontal {
    background-color: #00a8ff;
    border-radius: 3px;
}

/* 视频滑块样式 */
#videoSlider {
    height: 20px;
}

#videoSlider::groove:horizontal {
    border: none;
    height: 8px;
    background-color: #38444d;
    border-radius: 4px;
}

#videoSlider::handle:horizontal {
    background-color: #00a8ff;
    border: 2px solid #15202b;
    width: 16px;
    height: 16px;
    margin: -5px 0;
    border-radius: 8px;
}

#videoSlider::sub-page:horizontal {
    background-color: #00a8ff;
    border-radius: 4px;
}

/* 进度条样式 */
QProgressBar {
    border: 1px solid #38444d;
    border-radius: 6px;
    background-color: #15202b;
    text-align: center;
    color: #dfe6e9;
    height: 20px;
    font-weight: bold;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                               stop:0 #00a8ff,
                               stop:1 #00d2d3);
    border-radius: 5px;
}

/* 滚动条样式 */
QScrollBar:vertical {
    border: none;
    background: #10171e;
    width: 8px;
    margin: 0px;
    border-radius: 4px;
}

QScrollBar::handle:vertical {
    background: #00a8ff;
    border-radius: 4px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #0abde3;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: #10171e;
    height: 8px;
    margin: 0px;
    border-radius: 4px;
}

QScrollBar::handle:horizontal {
    background: #00a8ff;
    border-radius: 4px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background: #0abde3;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 视频显示标签 */
QLabel#videoDisplay {
    background-color: #10171e;
    color: #dfe6e9;
    border-radius: 6px;
    border: 1px solid #38444d;
    padding: 10px;
    font-size: 12px;
    font-weight: bold;
}

/* 状态栏样式 */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #15202b,
                               stop:1 #192734);
    color: #dfe6e9;
    border-top: 1px solid #38444d;
    min-height: 24px;
    padding: 2px 10px;
    font-weight: bold;
}

QStatusBar QLabel {
    color: #dfe6e9;
}

/* 工具提示样式 */
QToolTip {
    background-color: #192734;
    color: #dfe6e9;
    border: 1px solid #38444d;
    border-radius: 4px;
    padding: 5px;
}
