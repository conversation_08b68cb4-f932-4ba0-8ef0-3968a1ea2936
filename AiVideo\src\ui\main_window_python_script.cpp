#include <Python.h>
#include <QAction>
#include <QComboBox>
#include <QDateTime>
#include <QDialog>
#include <QDir>
#include <QFile>
#include <QFileDialog>
#include <QFontDatabase>
#include <QHBoxLayout>
#include <QLabel>
#include <QMenu>
#include <QMenuBar>
#include <QMessageBox>
#include <QPushButton>
#include <QTextEdit>
#include <QTextStream>
#include <QVBoxLayout>

#include "ai/plugins/python_script_manager.h"
#include "ai/plugins/ui/plugin_management_widget.h"
#include "ui/python_frame_processor_dialog.h"
#include "ui/python_script_dialog.h"
#include "ui/main_window.h"
#include "utils/dialog_utils.h"

namespace ui {

void MainWindow::openPythonScriptManager() {
    // 检查模型是否已加载
    if (!videoProcessingCore->get_model_manager()->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    try {
        // 确保Python脚本管理器已初始化
        if (!pythonScriptManager) {
            initializePythonScriptManager();
        }

        // 在打开对话框前加载脚本
        int loadedCount = pythonScriptManager->load_scripts_from_directory();

        // 将所有脚本注册到插件管理器
        int registeredCount = pythonScriptManager->register_all_scripts_to_plugin_manager(pluginManager);

        // 创建 Python 脚本管理对话框
        PythonScriptDialog dialog(videoProcessingCore->get_ai_processor(), this);

        // 显示对话框
        dialog.exec();
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("加载 Python 脚本时出错：%1").arg(e.what()),
            QMessageBox::Warning);
    }
}

void MainWindow::setupPythonScriptMenu() {
    // 获取设置菜单和工具菜单
    QMenu* settingsMenu = nullptr;
    QMenu* toolsMenu = nullptr;

    for (QAction* action : menuBar()->actions()) {
        if (action->menu() && action->text() == tr("设置")) {
            settingsMenu = action->menu();
        }
        else if (action->menu() && action->text() == tr("工具")) {
            toolsMenu = action->menu();
        }
    }

    if (!settingsMenu || !toolsMenu) {
        return;
    }

    // 添加分隔符
    settingsMenu->addSeparator();

    // 添加 Python 脚本管理菜单项到工具菜单
    QAction* pythonScriptAction = new QAction(tr("Python 脚本管理"), this);
    toolsMenu->addAction(pythonScriptAction);
    connect(pythonScriptAction, &QAction::triggered, this, &MainWindow::openPythonScriptManager);

    // 添加帧处理器子菜单
    QMenu* frameProcessorMenu = toolsMenu->addMenu(tr("帧处理器"));

    // 添加 Python 帧处理器配置菜单项
    QAction* pythonFrameProcessorAction = new QAction(tr("Python 帧处理器配置"), this);
    frameProcessorMenu->addAction(pythonFrameProcessorAction);
    connect(pythonFrameProcessorAction, &QAction::triggered, this, &MainWindow::openPythonFrameProcessorDialog);

    // 添加 C++ 帧处理器配置菜单项
    QAction* cppFrameProcessorAction = new QAction(tr("C++ 帧处理器配置"), this);
    frameProcessorMenu->addAction(cppFrameProcessorAction);
    connect(cppFrameProcessorAction, &QAction::triggered, this, &MainWindow::openCppFrameProcessorDialog);

    // 添加帧处理器类型切换菜单
    frameProcessorMenu->addSeparator();
    QAction* usePythonAction = new QAction(tr("使用 Python 帧处理器"), this);
    QAction* useCppAction = new QAction(tr("使用 C++ 帧处理器"), this);
    frameProcessorMenu->addAction(usePythonAction);
    frameProcessorMenu->addAction(useCppAction);
    connect(usePythonAction, &QAction::triggered, [this](){ toggleFrameProcessorType(false); });
    connect(useCppAction, &QAction::triggered, [this](){ toggleFrameProcessorType(true); });

    // 添加重新加载DLL插件菜单项
    frameProcessorMenu->addSeparator();
    QAction* reloadDllPluginsAction = new QAction(tr("重新加载 DLL 插件"), this);
    frameProcessorMenu->addAction(reloadDllPluginsAction);
    connect(reloadDllPluginsAction, &QAction::triggered, this, &MainWindow::reloadDllPlugins);

    // 添加帧处理脚本编辑器菜单项
    frameProcessorMenu->addSeparator();
    QAction* frameProcessorEditorAction = new QAction(tr("帧处理脚本编辑器"), this);
    frameProcessorMenu->addAction(frameProcessorEditorAction);
    connect(frameProcessorEditorAction, &QAction::triggered, this, &MainWindow::openFrameProcessorEditorDialog);

    // 设置脚本目录添加到设置菜单
    QAction* setScriptDirAction = new QAction(tr("设置脚本目录"), this);
    settingsMenu->addAction(setScriptDirAction);
    connect(setScriptDirAction, &QAction::triggered, this, &MainWindow::setScriptDirectory);
}

void MainWindow::setScriptDirectory() {
    // 创建一个对话框，让用户选择要设置的脚本目录类型
    QDialog dialog(this);
    dialog.setWindowTitle(tr("选择脚本目录类型"));
    dialog.setMinimumWidth(400);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    QLabel* label = new QLabel(tr("请选择要设置的脚本目录类型："), &dialog);
    layout->addWidget(label);

    QPushButton* taskPluginsButton = new QPushButton(tr("任务插件脚本目录"), &dialog);
    QPushButton* frameProcessorsButton = new QPushButton(tr("帧处理脚本目录"), &dialog);
    QPushButton* cancelButton = new QPushButton(tr("取消"), &dialog);

    layout->addWidget(taskPluginsButton);
    layout->addWidget(frameProcessorsButton);
    layout->addSpacing(10);
    layout->addWidget(cancelButton);

    connect(cancelButton, &QPushButton::clicked, &dialog, &QDialog::reject);

    // 设置任务插件脚本目录
    connect(taskPluginsButton, &QPushButton::clicked, [&dialog, this]() {
        // 确保Python脚本管理器已初始化
        if (!pythonScriptManager) {
            initializePythonScriptManager();
        }

        QString currentDir = QString::fromStdString(pythonScriptManager->get_script_directory());
        QString dir = QFileDialog::getExistingDirectory(
            this,
            tr("选择任务插件脚本目录"),
            currentDir,
            QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks
        );

        if (!dir.isEmpty()) {
            pythonScriptManager->set_script_directory(dir.toStdString());

            // 加载脚本目录中的脚本
            int loadedCount = pythonScriptManager->load_scripts_from_directory();

            // 将脚本注册到插件管理器
            int registeredCount = pythonScriptManager->register_all_scripts_to_plugin_manager(pluginManager);

            utils::showScrollableMessageBox(this, tr("成功"),
                tr("任务插件脚本目录已设置为：%1\n已加载 %2 个脚本，已注册 %3 个脚本。")
                    .arg(dir).arg(loadedCount).arg(registeredCount),
                QMessageBox::Information);

            dialog.accept();
        }
    });

    // 设置帧处理脚本目录
    connect(frameProcessorsButton, &QPushButton::clicked, [&dialog, this]() {
        QString currentDir = QString::fromStdString(videoProcessingCore->get_ai_processor()->get_python_frame_processor()->get_script_directory());
        QString dir = QFileDialog::getExistingDirectory(
            this,
            tr("选择帧处理脚本目录"),
            currentDir,
            QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks
        );

        if (!dir.isEmpty()) {
            videoProcessingCore->get_ai_processor()->get_python_frame_processor()->set_script_directory(dir.toStdString());

            utils::showScrollableMessageBox(this, tr("成功"),
                tr("帧处理脚本目录已设置为：%1").arg(dir),
                QMessageBox::Information);

            dialog.accept();
        }
    });

    dialog.exec();
}

void MainWindow::initializePythonScriptManager() {
    // 创建脚本目录（如果不存在）
    std::string taskPluginsDir = "plugins/task";
    std::string frameProcessorsDir = "plugins/frame_processors";

    if (!std::filesystem::exists(taskPluginsDir)) {
        std::filesystem::create_directories(taskPluginsDir);
    }

    if (!std::filesystem::exists(frameProcessorsDir)) {
        std::filesystem::create_directories(frameProcessorsDir);
    }

    // 确保插件管理器已初始化
    if (!pluginManager) {
        pluginManager = std::make_shared<ai::plugins::PluginManager>("plugins");
    }

    // 初始化Python脚本管理器
    if (!pythonScriptManager) {
        pythonScriptManager = std::make_shared<ai::plugins::PythonScriptManager>(taskPluginsDir);
    }

    // 设置脚本加载回调 - 但不立即加载脚本
    pythonScriptManager->set_script_load_callback(
        [this](const std::string& script_path, bool success) {
            if (!success) {
                auto script = pythonScriptManager->get_script(
                    std::filesystem::path(script_path).stem().string());
                if (script) {
                    QString errorMsg = QString::fromStdString(script->get_error_message());
                    utils::showScrollableMessageBox(this, tr("脚本加载失败"),
                        tr("脚本 %1 加载失败：\n%2").arg(QString::fromStdString(script_path)).arg(errorMsg),
                        QMessageBox::Warning);
                }
            }
        }
    );

    // 在启动时加载脚本并注册到插件管理器
    int loadedCount = pythonScriptManager->load_scripts_from_directory();
    int registeredCount = pythonScriptManager->register_all_scripts_to_plugin_manager(pluginManager);
}

void MainWindow::openPythonFrameProcessorDialog() {
    // 检查模型是否已加载
    if (!videoProcessingCore->get_model_manager()->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    try {
        // 获取 AI 处理器的 Python 帧处理器
        auto processor = videoProcessingCore->get_ai_processor()->get_python_frame_processor();
        if (!videoProcessingCore->get_ai_processor()) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("Python 帧处理器未初始化"),
                QMessageBox::Warning);
            return;
        }

        // 创建 Python 帧处理器对话框
        PythonFrameProcessorDialog dialog(videoProcessingCore->get_ai_processor()->get_python_frame_processor(), this);

        // 显示对话框
        if (dialog.exec() == QDialog::Accepted) {
            // 更新帧处理器状态标签
            updateFrameProcessorStatus();
        }
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("打开 Python 帧处理器对话框时出错：%1").arg(e.what()),
            QMessageBox::Warning);
    }
}

void MainWindow::updateFrameProcessorStatus() {
    std::cout << "Updating frame processor status..." << std::endl;
    // 获取AI处理器
    auto aiProcessor = videoProcessingCore->get_ai_processor();
    if (!aiProcessor) {
        frameProcessorStatusLabel->setText(tr("帧处理器: 未初始化"));
        frameProcessorStatusLabel->setStyleSheet("color: gray;");
        std::cout << "AI processor is null" << std::endl;
        return;
    }

    // 检查是否使用C++帧处理器
    bool useCpp = aiProcessor->is_using_cpp_frame_processor();
    std::cout << "Using C++ frame processor: " << (useCpp ? "true" : "false") << std::endl;

    if (useCpp) {
        // 获取C++帧处理器
        auto processor = aiProcessor->get_cpp_frame_processor();
        if (!processor) {
            frameProcessorStatusLabel->setText(tr("帧处理器: C++ (未初始化)"));
            frameProcessorStatusLabel->setStyleSheet("color: gray;");
            std::cout << "C++ processor is null" << std::endl;
            return;
        }

        // 检查是否启用
        bool isEnabled = processor->is_enabled();
        std::cout << "C++ processor enabled: " << (isEnabled ? "true" : "false") << std::endl;

        // 获取当前插件名称
        std::string pluginName = processor->get_current_plugin_name();
        std::cout << "Current plugin name: '" << pluginName << "'" << std::endl;

        // 获取当前插件并检查其启用状态
        auto plugin = processor->get_plugin(pluginName);
        bool pluginEnabled = plugin ? plugin->is_enabled() : false;

        // 获取所有可用的插件名称
        auto pluginNames = processor->get_plugin_names();

        // 更新状态标签
        if (isEnabled && !pluginName.empty()) {
            if (pluginEnabled) {
                frameProcessorStatusLabel->setText(tr("帧处理器: C++ 已启用 (%1)").arg(QString::fromStdString(pluginName)));
                frameProcessorStatusLabel->setStyleSheet("color: green;");
            } else {
                frameProcessorStatusLabel->setText(tr("帧处理器: C++ 已启用 (%1 插件未启用)").arg(QString::fromStdString(pluginName)));
                frameProcessorStatusLabel->setStyleSheet("color: orange;");
            }
        } else if (!pluginName.empty()) {
            frameProcessorStatusLabel->setText(tr("帧处理器: C++ 未启用 (%1)").arg(QString::fromStdString(pluginName)));
            frameProcessorStatusLabel->setStyleSheet("color: orange;");
        } else {
            frameProcessorStatusLabel->setText(tr("帧处理器: C++ (未配置)"));
            frameProcessorStatusLabel->setStyleSheet("color: gray;");
        }
    } else {
        // 获取Python帧处理器
        auto processor = aiProcessor->get_python_frame_processor();
        if (!processor) {
            frameProcessorStatusLabel->setText(tr("帧处理器: Python (未初始化)"));
            frameProcessorStatusLabel->setStyleSheet("color: gray;");
            return;
        }

        // 检查是否启用
        bool isEnabled = processor->is_enabled();

        // 获取脚本路径
        std::string scriptPath = processor->get_script_path();

        // 更新状态标签
        if (isEnabled && !scriptPath.empty()) {
            // 获取脚本文件名
            std::string scriptName = std::filesystem::path(scriptPath).filename().string();
            frameProcessorStatusLabel->setText(tr("帧处理器: Python 已启用 (%1)").arg(QString::fromStdString(scriptName)));
            frameProcessorStatusLabel->setStyleSheet("color: green;");
        } else if (!scriptPath.empty()) {
            std::string scriptName = std::filesystem::path(scriptPath).filename().string();
            frameProcessorStatusLabel->setText(tr("帧处理器: Python 未启用 (%1)").arg(QString::fromStdString(scriptName)));
            frameProcessorStatusLabel->setStyleSheet("color: orange;");
        } else {
            frameProcessorStatusLabel->setText(tr("帧处理器: Python (未配置)"));
            frameProcessorStatusLabel->setStyleSheet("color: gray;");
        }
    }
}

void MainWindow::openFrameProcessorEditorDialog() {
    // 检查模型是否已加载
    if (!videoProcessingCore->get_model_manager()->is_model_loaded()) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("请先加载AI模型！"),
            QMessageBox::Warning);
        return;
    }

    // 检查是否使用C++帧处理器
    if (videoProcessingCore->get_ai_processor() &&
        videoProcessingCore->get_ai_processor()->is_using_cpp_frame_processor()) {
        utils::showScrollableMessageBox(this, tr("提示"),
            tr("C++帧处理器不支持脚本编辑器功能。\n"
               "请先切换到Python帧处理器或使用C++帧处理器配置对话框。"),
            QMessageBox::Information);
        return;
    }

    try {
        // 获取 AI 处理器的 Python 帧处理器
        auto processor = videoProcessingCore->get_ai_processor()->get_python_frame_processor();
        if (!processor) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("Python 帧处理器未初始化"),
                QMessageBox::Warning);
            return;
        }

        // 获取当前脚本路径
        std::string scriptPath = processor->get_script_path();
        std::string scriptDir = processor->get_script_directory();

        // 创建对话框
        QDialog dialog(this);
        dialog.setWindowTitle(tr("帧处理脚本编辑器"));
        dialog.setMinimumSize(800, 600);

        // 创建主布局
        QVBoxLayout* mainLayout = new QVBoxLayout(&dialog);

        // 创建脚本选择区域
        QHBoxLayout* scriptSelectionLayout = new QHBoxLayout();
        QLabel* scriptLabel = new QLabel(tr("选择脚本:"), &dialog);
        QComboBox* scriptComboBox = new QComboBox(&dialog);
        scriptComboBox->setMinimumWidth(300);

        QPushButton* loadButton = new QPushButton(tr("加载"), &dialog);
        QPushButton* saveButton = new QPushButton(tr("保存"), &dialog);
        QPushButton* reloadButton = new QPushButton(tr("重新加载"), &dialog);
        QPushButton* newButton = new QPushButton(tr("新建"), &dialog);

        scriptSelectionLayout->addWidget(scriptLabel);
        scriptSelectionLayout->addWidget(scriptComboBox);
        scriptSelectionLayout->addWidget(loadButton);
        scriptSelectionLayout->addWidget(saveButton);
        scriptSelectionLayout->addWidget(reloadButton);
        scriptSelectionLayout->addWidget(newButton);
        scriptSelectionLayout->addStretch();

        mainLayout->addLayout(scriptSelectionLayout);

        // 创建脚本编辑器
        QTextEdit* scriptEditor = new QTextEdit(&dialog);

        // 设置等宽字体
        QFont font = QFontDatabase::systemFont(QFontDatabase::FixedFont);
        font.setPointSize(10);
        scriptEditor->setFont(font);

        mainLayout->addWidget(scriptEditor);

        // 创建状态标签
        QLabel* statusLabel = new QLabel(&dialog);
        mainLayout->addWidget(statusLabel);

        // 创建按钮区域
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        QPushButton* closeButton = new QPushButton(tr("关闭"), &dialog);
        buttonLayout->addStretch();
        buttonLayout->addWidget(closeButton);

        mainLayout->addLayout(buttonLayout);

        // 更新脚本列表的函数
        auto updateScriptList = [scriptComboBox, scriptDir]() {
            scriptComboBox->clear();

            QDir dir(QString::fromStdString(scriptDir));
            QStringList filters;
            filters << "*.py";
            dir.setNameFilters(filters);

            QStringList scriptFiles = dir.entryList(filters, QDir::Files, QDir::Name);
            scriptComboBox->addItems(scriptFiles);
        };

        // 加载脚本内容的函数
        auto loadScriptContent = [scriptEditor, statusLabel](const QString& filePath) {
            QFile file(filePath);
            if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
                QTextStream in(&file);
                in.setCodec("UTF-8");
                scriptEditor->setPlainText(in.readAll());
                file.close();

                statusLabel->setText(QObject::tr("已加载脚本: %1").arg(filePath));
                return true;
            } else {
                statusLabel->setText(QObject::tr("无法打开脚本文件: %1").arg(filePath));
                return false;
            }
        };

        // 初始化脚本列表
        updateScriptList();

        // 当前脚本路径
        QString currentScriptPath = QString::fromStdString(scriptPath);

        // 加载当前脚本
        if (!currentScriptPath.isEmpty() && QFile::exists(currentScriptPath)) {
            loadScriptContent(currentScriptPath);

            // 设置当前选中的脚本
            int index = scriptComboBox->findText(QFileInfo(currentScriptPath).fileName());
            if (index >= 0) {
                scriptComboBox->setCurrentIndex(index);
            }
        }

        // 连接信号和槽
        QObject::connect(loadButton, &QPushButton::clicked, [&dialog, scriptComboBox, scriptEditor, statusLabel, &currentScriptPath, scriptDir, loadScriptContent, updateScriptList]() {
            QString filePath = QFileDialog::getOpenFileName(
                &dialog,
                QObject::tr("选择Python脚本"),
                QString::fromStdString(scriptDir),
                QObject::tr("Python Files (*.py)")
            );

            if (!filePath.isEmpty()) {
                if (loadScriptContent(filePath)) {
                    currentScriptPath = filePath;

                    // 更新脚本列表
                    updateScriptList();

                    // 设置当前选中的脚本
                    int index = scriptComboBox->findText(QFileInfo(filePath).fileName());
                    if (index >= 0) {
                        scriptComboBox->setCurrentIndex(index);
                    }
                }
            }
        });

        QObject::connect(saveButton, &QPushButton::clicked, [&dialog, scriptEditor, statusLabel, &currentScriptPath, scriptDir, updateScriptList, scriptComboBox]() {
            if (currentScriptPath.isEmpty()) {
                // 如果没有当前脚本路径，则执行另存为操作
                QString filePath = QFileDialog::getSaveFileName(
                    &dialog,
                    QObject::tr("保存Python脚本"),
                    QString::fromStdString(scriptDir),
                    QObject::tr("Python Files (*.py)")
                );

                if (!filePath.isEmpty()) {
                    currentScriptPath = filePath;
                } else {
                    return;
                }
            }

            QFile file(currentScriptPath);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out.setCodec("UTF-8");
                out << scriptEditor->toPlainText();
                file.close();

                statusLabel->setText(QObject::tr("脚本已保存: %1").arg(currentScriptPath));

                // 更新脚本列表
                updateScriptList();

                // 设置当前选中的脚本
                int index = scriptComboBox->findText(QFileInfo(currentScriptPath).fileName());
                if (index >= 0) {
                    scriptComboBox->setCurrentIndex(index);
                }
            } else {
                statusLabel->setText(QObject::tr("无法保存脚本文件: %1").arg(currentScriptPath));
            }
        });

        QObject::connect(reloadButton, &QPushButton::clicked, [&dialog, saveButton, processor, statusLabel, &currentScriptPath]() {
            // 先保存当前脚本
            saveButton->click();

            // 然后重新加载脚本
            if (processor->load_script(currentScriptPath.toStdString())) {
                statusLabel->setText(QObject::tr("脚本已重新加载: %1").arg(currentScriptPath));
                QMessageBox::information(&dialog, QObject::tr("成功"), QObject::tr("脚本已重新加载。"));
            } else {
                statusLabel->setText(QObject::tr("重新加载脚本失败: %1").arg(QString::fromStdString(processor->get_error_message())));
                QMessageBox::warning(&dialog, QObject::tr("错误"),
                    QObject::tr("重新加载失败: %1").arg(QString::fromStdString(processor->get_error_message())));
            }
        });

        QObject::connect(newButton, &QPushButton::clicked, [&dialog, scriptEditor, statusLabel, &currentScriptPath, scriptDir, scriptComboBox, updateScriptList]() {
            // 创建新脚本文件名
            QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
            QString newFileName = QString("frame_processor_%1.py").arg(timestamp);
            QString newFilePath = QDir(QString::fromStdString(scriptDir)).filePath(newFileName);

            // 创建新脚本内容
            QString templateContent = R"(# -*- coding: utf-8 -*-
"""
自定义帧处理脚本
创建时间: %1
"""

import numpy as np
import cv2

# 全局变量
params = {}
required_frames = 5  # 默认需要5帧

def initialize(parameters):
    """初始化函数"""
    global params, required_frames
    params = parameters

    # 从参数中读取需要的帧数（如果有）
    if 'required_frames' in parameters:
        try:
            required_frames = int(parameters['required_frames'])
            # 确保帧数在合理范围内
            if required_frames < 1:
                required_frames = 1
            elif required_frames > 100:
                required_frames = 100
        except:
            required_frames = 5  # 如果转换失败，使用默认值

    # 从参数中读取处理模式
    if 'mode' not in parameters:
        params['mode'] = 'original'  # 默认使用原始模式

    print(f"帧处理脚本初始化，需要帧数: {required_frames}，处理模式: {params['mode']}")

def get_required_frames():
    """返回脚本需要处理的帧数"""
    global required_frames
    return required_frames

def process_frames(frames):
    """处理多帧图像，返回一个处理后的图像"""
    global params

    # 打印收到的帧数
    print(f"收到 {len(frames)} 帧进行处理")

    # 如果没有帧，返回空图像
    if len(frames) == 0:
        return np.zeros((100, 100, 3), dtype=np.uint8)

    # 如果只有一帧，直接返回
    if len(frames) == 1:
        return frames[0]

    # 获取处理模式
    mode = params.get('mode', 'original')

    # 根据不同模式处理帧
    if mode == 'original':
        # 原始模式：直接返回当前帧
        return frames[0]

    elif mode == 'grayscale':
        # 灰度模式：转换为灰度图
        gray = cv2.cvtColor(frames[0], cv2.COLOR_BGR2GRAY)
        return cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)

    elif mode == 'blur':
        # 模糊模式
        kernel_size = int(params.get('kernel_size', '5'))
        return cv2.GaussianBlur(frames[0], (kernel_size, kernel_size), 0)

    else:
        # 默认返回第一帧
        return frames[0]
)";

            // 替换时间戳
            templateContent = templateContent.arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss"));

            // 保存新脚本
            QFile file(newFilePath);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out.setCodec("UTF-8");
                out << templateContent;
                file.close();

                // 加载新脚本
                scriptEditor->setPlainText(templateContent);
                currentScriptPath = newFilePath;

                // 更新脚本列表
                updateScriptList();

                // 设置当前选中的脚本
                int index = scriptComboBox->findText(QFileInfo(newFilePath).fileName());
                if (index >= 0) {
                    scriptComboBox->setCurrentIndex(index);
                }

                statusLabel->setText(QObject::tr("已创建新脚本: %1").arg(newFilePath));
            } else {
                statusLabel->setText(QObject::tr("无法创建新脚本: %1").arg(newFilePath));
            }
        });

        QObject::connect(scriptComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            [scriptComboBox, scriptDir, loadScriptContent, &currentScriptPath](int index) {
                if (index >= 0) {
                    QString scriptName = scriptComboBox->itemText(index);
                    QString scriptPath = QDir(QString::fromStdString(scriptDir)).filePath(scriptName);

                    // 加载选中的脚本
                    if (loadScriptContent(scriptPath)) {
                        currentScriptPath = scriptPath;
                    }
                }
            });

        QObject::connect(closeButton, &QPushButton::clicked, &dialog, &QDialog::accept);

        // 显示对话框
        dialog.exec();
    } catch (const std::exception& e) {
        utils::showScrollableMessageBox(this, tr("错误"),
            tr("打开帧处理脚本编辑器对话框时出错：%1").arg(e.what()),
            QMessageBox::Warning);
    }
}

} // namespace ui





