#pragma once

#include <QPushButton>
#include <QStackedWidget>
#include <QTreeWidget>
#include <QVBoxLayout>
#include <QWidget>

#include "ai/plugins/task_plugin.h"
#include "ai/plugins/plugin_manager.h"

namespace ai {
namespace plugins {
namespace ui {

class PluginManagementWidget : public QWidget {
    Q_OBJECT

public:
    explicit PluginManagementWidget(std::shared_ptr<ai::plugins::PluginManager> plugin_manager, QWidget* parent = nullptr);

private slots:
    void onPluginSelected(QTreeWidgetItem* current, QTreeWidgetItem* previous);
    void onEnableButtonClicked();
    void onAddPythonPluginClicked();
    void onAddDllPluginClicked();
    void refreshPluginList();

private:
    void setupUI();
    void initializePluginTree();
    void addPluginToTree(const std::shared_ptr<ai::plugins::TaskPlugin>& plugin);

    QTreeWidget* pluginTree;
    QStackedWidget* configStack;
    QPushButton* enableButton;
    QPushButton* addPythonPluginButton;
    QPushButton* addDllPluginButton;

    // 当前选中的插件
    std::shared_ptr<ai::plugins::TaskPlugin> currentPlugin;

    // 插件管理器
    std::shared_ptr<ai::plugins::PluginManager> plugin_manager_;
};

} // namespace ui
} // namespace plugins
} // namespace ai
