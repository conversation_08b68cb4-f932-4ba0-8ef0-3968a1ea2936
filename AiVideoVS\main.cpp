#include <QApplication>
#include <QDir>
#include <QDebug>
#include <QTextCodec>
#include <QFile>
#include <QStyleFactory>

#include "mainwindow.h"

#ifdef _WIN32
#include <windows.h>
#endif

int main(int argc, char *argv[])
{
    // 由于使用WIN32应用程序，不再需要设置控制台编码

    // 设置Qt消息处理，确保日志输出正确编码
    qSetMessagePattern("%{time yyyy-MM-dd hh:mm:ss.zzz} [%{type}] %{message}");

    // 创建Qt应用程序
    QApplication app(argc, argv);

    // 设置应用程序信息
    QApplication::setApplicationName("AiVideoVS");
    QApplication::setApplicationVersion("1.0.0");
    QApplication::setOrganizationName("AiVideoVS");

    // 设置文本编码
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));

    // 设置应用程序样式
    qApp->setStyle(QStyleFactory::create("Fusion"));

    // 加载样式表
    QFile styleFile(":/styles.qss");
    if (styleFile.open(QFile::ReadOnly)) {
        QString styleSheet = QLatin1String(styleFile.readAll());
        app.setStyleSheet(styleSheet);
        styleFile.close();
        qDebug() << "已加载自定义样式表";
    } else {
        qWarning() << "无法加载样式表:" << styleFile.errorString();
    }

    // 创建并显示主窗口
    MainWindow mainWindow;
    mainWindow.show();

    // 运行应用程序
    return app.exec();
}
