#include "videostreamwidget.h"

// 辅助函数：将OpenCV的Mat转换为QImage
QImage mat_to_qimage(const cv::Mat& mat) {
    // 处理单通道图像（灰度图）
    if (mat.type() == CV_8UC1) {
        // 创建一个 QImage 对象，直接使用 cv::Mat 的数据
        QImage image(mat.data, mat.cols, mat.rows, mat.step, QImage::Format_Grayscale8);
        // 返回 QImage 的深拷贝，避免数据生命周期问题
        return image.copy();
    }
    // 处理三通道图像（BGR 格式）
    else if (mat.type() == CV_8UC3) {
        // 转换颜色空间
        cv::Mat rgbMat;
        cv::cvtColor(mat, rgbMat, cv::COLOR_BGR2RGB);
        QImage image(rgbMat.data, rgbMat.cols, rgbMat.rows, rgbMat.step, QImage::Format_RGB888);
        // 返回 QImage 的深拷贝，避免数据生命周期问题
        return image.copy();
    }
    // 处理四通道图像（BGRA 格式）
    else if (mat.type() == CV_8UC4) {
        // 创建一个 QImage 对象，使用 cv::Mat 的数据，并转换为 ARGB 格式
        cv::Mat rgbaMat;
        cv::cvtColor(mat, rgbaMat, cv::COLOR_BGRA2RGBA);
        QImage image(rgbaMat.data, rgbaMat.cols, rgbaMat.rows, rgbaMat.step, QImage::Format_RGBA8888);
        // 返回 QImage 的深拷贝，避免数据生命周期问题
        return image.copy();
    }
    // 其他类型的图像，返回一个空的 QImage
    else {
        return QImage();
    }
}

VideoStreamWidget::VideoStreamWidget(QWidget *parent)
    : QWidget(parent),
      streamId(-1),
      resultStoragePort(8888),
      hasResult(false),
      fullscreenDialog(nullptr)
{
    // 创建布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 创建视频视图
    videoView = new QGraphicsView(this);
    videoView->setAlignment(Qt::AlignCenter);
    videoView->setMinimumSize(320, 240);
    videoView->setObjectName("videoView"); // 设置对象名，以便在样式表中定位
    videoView->setRenderHint(QPainter::Antialiasing);
    videoView->setRenderHint(QPainter::SmoothPixmapTransform);
    videoView->setRenderHint(QPainter::TextAntialiasing);
    videoView->setViewportUpdateMode(QGraphicsView::FullViewportUpdate);
    videoView->setOptimizationFlags(QGraphicsView::DontSavePainterState);
    videoView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    videoView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    videoView->setFrameShape(QFrame::NoFrame);

    // 创建场景
    QGraphicsScene* scene = new QGraphicsScene(this);
    videoView->setScene(scene);

    // 添加文本提示
    QGraphicsTextItem* textItem = scene->addText("无视频信号");
    textItem->setDefaultTextColor(Qt::white);
    textItem->setPos(10, 10);

    // 创建信息标签
    infoLabel = new QLabel(this);
    infoLabel->setAlignment(Qt::AlignLeft);
    infoLabel->setText("未配置");

    // 创建控制按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    startButton = new QPushButton("启动", this);
    pauseButton = new QPushButton("暂停", this);
    stopButton = new QPushButton("停止", this);
    fullscreenButton = new QPushButton("全屏", this);

    // 设置按钮图标
    startButton->setIcon(QIcon(":/icons/play.png"));
    pauseButton->setIcon(QIcon(":/icons/pause.png"));
    stopButton->setIcon(QIcon(":/icons/stop.png"));
    fullscreenButton->setIcon(QIcon(":/icons/fullscreen.png"));

    // 添加按钮到布局
    buttonLayout->addWidget(startButton);
    buttonLayout->addWidget(pauseButton);
    buttonLayout->addWidget(stopButton);
    buttonLayout->addWidget(fullscreenButton);

    // 添加控件到主布局
    mainLayout->addWidget(videoView, 1);
    mainLayout->addWidget(infoLabel, 0);
    mainLayout->addLayout(buttonLayout, 0);

    // 初始化渲染器
    pluginRenderer = std::make_unique<utils::QPluginRenderer>();

    // 连接信号和槽
    connect(startButton, &QPushButton::clicked, this, &VideoStreamWidget::onStartButtonClicked);
    connect(pauseButton, &QPushButton::clicked, this, &VideoStreamWidget::onPauseButtonClicked);
    connect(stopButton, &QPushButton::clicked, this, &VideoStreamWidget::onStopButtonClicked);
    connect(fullscreenButton, &QPushButton::clicked, this, &VideoStreamWidget::onFullscreenButtonClicked);

    // 设置控件大小策略
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
}

VideoStreamWidget::~VideoStreamWidget()
{
    // 如果全屏对话框存在，关闭并删除它
    if (fullscreenDialog) {
        fullscreenDialog->close();
        delete fullscreenDialog;
        fullscreenDialog = nullptr;
    }
}

void VideoStreamWidget::setStreamId(int id)
{
    streamId = id;
}

void VideoStreamWidget::setStreamInfo(const QString &videoPath, const QString &projectPath, int port)
{
    this->videoPath = videoPath;
    this->projectPath = projectPath;
    this->resultStoragePort = port;

    // 更新信息标签
    QString info = QString("视频流 #%1\n").arg(streamId + 1);
    info += QString("视频: %1\n").arg(videoPath.isEmpty() ? "未设置" : videoPath);
    info += QString("项目: %1\n").arg(projectPath.isEmpty() ? "未设置" : projectPath);
    info += QString("端口: %1").arg(port);

    infoLabel->setText(info);
}

void VideoStreamWidget::updateFrame(const cv::Mat &frame)
{
    if (frame.empty()) {
        return;
    }

    // 保存当前帧的副本
    currentFrame = frame.clone();
    hasResult = false;

    // 检查是否已有场景，如果有则重用
    QGraphicsScene* scene = videoView->scene();
    bool newSceneCreated = false;

    if (!scene) {
        scene = new QGraphicsScene(this);
        newSceneCreated = true;
    } else {
        // 清除现有场景中的所有项目
        scene->clear();
    }

    // 将OpenCV图像转换为QImage并添加到场景
    QImage image = mat_to_qimage(currentFrame);
    if (!image.isNull()) {
        QGraphicsPixmapItem* pixmapItem = scene->addPixmap(QPixmap::fromImage(image));
        pixmapItem->setZValue(0); // 设置为背景层

        // 如果是新创建的场景，设置到视图
        if (newSceneCreated) {
            videoView->setScene(scene);
        }

        // 更新场景矩形和视图
        scene->setSceneRect(0, 0, currentFrame.cols, currentFrame.rows);
        videoView->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);
    } else if (newSceneCreated) {
        // 如果是新创建的场景但图像无效，删除场景
        delete scene;
    }

    // 如果全屏对话框存在且可见，使用原始帧更新全屏对话框中的图像
    if (fullscreenDialog && fullscreenDialog->isVisible()) {
        fullscreenDialog->updateFromMat(currentFrame);
    }
}

void VideoStreamWidget::updateFrameWithResult(const cv::Mat &frame, const ai::FrameResult &result)
{
    if (frame.empty()) {
        return;
    }

    // 保存当前帧和结果
    currentFrame = frame.clone();
    currentResult = result;
    hasResult = !result.ext_info.empty();

    // 检查是否已有场景，如果有则重用
    QGraphicsScene* scene = videoView->scene();
    bool newSceneCreated = false;

    if (!scene) {
        scene = new QGraphicsScene(this);
        newSceneCreated = true;
    } else {
        // 清除现有场景中的所有项目
        scene->clear();
    }

    // 将OpenCV图像转换为QImage并添加到场景
    QImage image = mat_to_qimage(currentFrame);
    if (!image.isNull()) {
        QGraphicsPixmapItem* pixmapItem = scene->addPixmap(QPixmap::fromImage(image));
        pixmapItem->setZValue(0); // 设置为背景层

        // 如果是新创建的场景，设置到视图
        if (newSceneCreated) {
            videoView->setScene(scene);
        }

        // 更新场景矩形和视图
        scene->setSceneRect(0, 0, currentFrame.cols, currentFrame.rows);
        videoView->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);

        // 如果有渲染信息，在图像上方渲染图形元素
        if (hasResult) {
            // 使用QPluginRenderer直接渲染到视图
            QSize frameSize(currentFrame.cols, currentFrame.rows);
            pluginRenderer->render_graphics_frame_result_to_view(videoView, frameSize, currentResult);
        }
    } else if (newSceneCreated) {
        // 如果是新创建的场景但图像无效，删除场景
        delete scene;
    }

    // 如果全屏对话框存在且可见，使用原始帧和结果更新全屏对话框中的图像
    if (fullscreenDialog && fullscreenDialog->isVisible()) {
        fullscreenDialog->updateFromMatWithResult(currentFrame, currentResult);
    }
}

int VideoStreamWidget::getStreamId() const
{
    return streamId;
}

void VideoStreamWidget::onStartButtonClicked()
{
    emit startStream(streamId);
}

void VideoStreamWidget::onPauseButtonClicked()
{
    emit pauseStream(streamId);
}

void VideoStreamWidget::onStopButtonClicked()
{
    emit stopStream(streamId);
}

void VideoStreamWidget::onFullscreenButtonClicked()
{
    // 检查是否有当前帧
    if (currentFrame.empty()) {
        return;
    }

    // 如果已经有全屏对话框，先关闭它
    if (fullscreenDialog) {
        fullscreenDialog->close();
        delete fullscreenDialog;
        fullscreenDialog = nullptr;
    }

    // 创建新的全屏对话框
    fullscreenDialog = new FullscreenDialog(this);

    // 更新图像
    if (hasResult) {
        fullscreenDialog->updateFromMatWithResult(currentFrame, currentResult);
    } else {
        fullscreenDialog->updateFromMat(currentFrame);
    }

    // 显示全屏对话框（非模态）
    fullscreenDialog->showFullScreen();
}



// 全屏对话框实现
VideoStreamWidget::FullscreenDialog::FullscreenDialog(QWidget *parent) : QDialog(parent), hasResult(false)
{
    // 设置窗口属性
    setWindowFlags(Qt::Window | Qt::FramelessWindowHint);

    // 获取屏幕尺寸
    QScreen *screen = QGuiApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();

    // 设置对话框大小为屏幕大小
    resize(screenGeometry.width(), screenGeometry.height());
    move(0, 0);

    // 创建布局
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);

    // 创建视频视图
    videoView = new QGraphicsView(this);
    videoView->setAlignment(Qt::AlignCenter);
    videoView->setObjectName("videoView"); // 设置对象名，以便在样式表中定位
    videoView->setRenderHint(QPainter::Antialiasing);
    videoView->setRenderHint(QPainter::SmoothPixmapTransform);
    videoView->setRenderHint(QPainter::TextAntialiasing);
    videoView->setViewportUpdateMode(QGraphicsView::FullViewportUpdate);
    videoView->setOptimizationFlags(QGraphicsView::DontSavePainterState);
    videoView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    videoView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    videoView->setFrameShape(QFrame::NoFrame);

    // 创建场景
    QGraphicsScene* scene = new QGraphicsScene(this);
    videoView->setScene(scene);

    // 添加到布局
    layout->addWidget(videoView);

    // 设置提示信息
    setToolTip("按ESC键退出全屏");

    // 添加提示标签
    QLabel *tipLabel = new QLabel("按ESC键退出全屏", this);
    tipLabel->setObjectName("tipLabel");
    tipLabel->setStyleSheet("QLabel#tipLabel { color: white; background-color: rgba(0, 0, 0, 128); padding: 5px; border-radius: 4px; }");
    tipLabel->setAlignment(Qt::AlignCenter);

    // 将提示标签添加到布局底部
    layout->addWidget(tipLabel, 0, Qt::AlignBottom | Qt::AlignHCenter);

    // 初始化渲染器
    pluginRenderer = std::make_unique<utils::QPluginRenderer>();
}

VideoStreamWidget::FullscreenDialog::~FullscreenDialog()
{
    // 通知父窗口对话框已关闭
    if (parent()) {
        VideoStreamWidget *widget = qobject_cast<VideoStreamWidget*>(parent());
        if (widget) {
            widget->fullscreenDialog = nullptr;
        }
    }
}

void VideoStreamWidget::FullscreenDialog::updateFromMat(const cv::Mat &frame)
{
    if (frame.empty()) {
        return;
    }

    // 保存当前帧
    currentFrame = frame.clone();
    hasResult = false;

    // 检查是否已有场景，如果有则重用
    QGraphicsScene* scene = videoView->scene();
    bool newSceneCreated = false;

    if (!scene) {
        scene = new QGraphicsScene(this);
        newSceneCreated = true;
    } else {
        // 清除现有场景中的所有项目
        scene->clear();
    }

    // 将OpenCV图像转换为QImage并添加到场景
    QImage image = mat_to_qimage(currentFrame);
    if (!image.isNull()) {
        QGraphicsPixmapItem* pixmapItem = scene->addPixmap(QPixmap::fromImage(image));
        pixmapItem->setZValue(0); // 设置为背景层

        // 如果是新创建的场景，设置到视图
        if (newSceneCreated) {
            videoView->setScene(scene);
        }

        // 更新场景矩形和视图
        scene->setSceneRect(0, 0, currentFrame.cols, currentFrame.rows);
        videoView->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);
    } else if (newSceneCreated) {
        // 如果是新创建的场景但图像无效，删除场景
        delete scene;
    }
}

void VideoStreamWidget::FullscreenDialog::updateFromMatWithResult(const cv::Mat &frame, const ai::FrameResult &result)
{
    if (frame.empty()) {
        return;
    }

    // 保存当前帧和结果
    currentFrame = frame.clone();
    currentResult = result;
    hasResult = !result.ext_info.empty();

    // 检查是否已有场景，如果有则重用
    QGraphicsScene* scene = videoView->scene();
    bool newSceneCreated = false;

    if (!scene) {
        scene = new QGraphicsScene(this);
        newSceneCreated = true;
    } else {
        // 清除现有场景中的所有项目
        scene->clear();
    }

    // 将OpenCV图像转换为QImage并添加到场景
    QImage image = mat_to_qimage(currentFrame);
    if (!image.isNull()) {
        QGraphicsPixmapItem* pixmapItem = scene->addPixmap(QPixmap::fromImage(image));
        pixmapItem->setZValue(0); // 设置为背景层

        // 如果是新创建的场景，设置到视图
        if (newSceneCreated) {
            videoView->setScene(scene);
        }

        // 更新场景矩形和视图
        scene->setSceneRect(0, 0, currentFrame.cols, currentFrame.rows);
        videoView->fitInView(scene->sceneRect(), Qt::KeepAspectRatio);

        // 如果有渲染信息，在图像上方渲染图形元素
        if (hasResult) {
            // 使用QPluginRenderer直接渲染到视图
            QSize frameSize(currentFrame.cols, currentFrame.rows);
            pluginRenderer->render_graphics_frame_result_to_view(videoView, frameSize, currentResult);
        }
    } else if (newSceneCreated) {
        // 如果是新创建的场景但图像无效，删除场景
        delete scene;
    }
}

void VideoStreamWidget::FullscreenDialog::resizeEvent(QResizeEvent *event)
{
    QDialog::resizeEvent(event);

    // 窗口大小变化时，重新调整视图
    if (videoView && videoView->scene()) {
        // 设置视图大小为对话框大小
        videoView->resize(width(), height());

        // 调整视图以适应场景
        videoView->fitInView(videoView->scene()->sceneRect(), Qt::KeepAspectRatio);
    }

    // 如果有当前帧，重新渲染
    if (!currentFrame.empty()) {
        if (hasResult) {
            updateFromMatWithResult(currentFrame, currentResult);
        } else {
            updateFromMat(currentFrame);
        }
    }
}

void VideoStreamWidget::FullscreenDialog::keyPressEvent(QKeyEvent *event)
{
    // 按ESC键退出全屏
    if (event->key() == Qt::Key_Escape) {
        close();
    } else {
        QDialog::keyPressEvent(event);
    }
}

void VideoStreamWidget::FullscreenDialog::closeEvent(QCloseEvent *event)
{
    // 通知父窗口对话框已关闭
    if (parent()) {
        VideoStreamWidget *widget = qobject_cast<VideoStreamWidget*>(parent());
        if (widget) {
            widget->fullscreenDialog = nullptr;
        }
    }

    QDialog::closeEvent(event);
}

void VideoStreamWidget::FullscreenDialog::showEvent(QShowEvent *event)
{
    QDialog::showEvent(event);

    // 确保对话框是全屏的
    showFullScreen();

    // 获取屏幕尺寸
    QScreen *screen = QGuiApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();

    // 设置对话框大小为屏幕大小
    resize(screenGeometry.width(), screenGeometry.height());
    move(0, 0);

    // 调整视图大小
    if (videoView) {
        videoView->resize(width(), height());

        // 如果有场景，调整视图以适应场景
        if (videoView->scene()) {
            videoView->fitInView(videoView->scene()->sceneRect(), Qt::KeepAspectRatio);
        }
    }
}
