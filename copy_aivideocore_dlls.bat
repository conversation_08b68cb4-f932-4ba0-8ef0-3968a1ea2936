@echo off
echo Copying AiVideoCore DLLs to AiVideo release directory...

set SOURCE_DIR=D:\yupei.wu\AiVideoCore\bin
set DEST_DIR=D:\yupei.wu\AiVideo\bin\release

if not exist "%SOURCE_DIR%" (
    echo Error: Source directory %SOURCE_DIR% does not exist!
    exit /b 1
)

if not exist "%DEST_DIR%" (
    echo Creating destination directory %DEST_DIR%...
    mkdir "%DEST_DIR%"
)

echo Copying core DLLs...
copy /Y "%SOURCE_DIR%\VideoProcessingCore.dll" "%DEST_DIR%"
copy /Y "%SOURCE_DIR%\ai.dll" "%DEST_DIR%"
copy /Y "%SOURCE_DIR%\core.dll" "%DEST_DIR%"
copy /Y "%SOURCE_DIR%\tracking.dll" "%DEST_DIR%"
copy /Y "%SOURCE_DIR%\utils.dll" "%DEST_DIR%"

echo Copying dependency DLLs...
if exist "%SOURCE_DIR%\lib\*.dll" (
    copy /Y "%SOURCE_DIR%\lib\*.dll" "%DEST_DIR%"
)

echo Copying additional DLLs...
if exist "%SOURCE_DIR%\*.dll" (
    for %%F in ("%SOURCE_DIR%\*.dll") do (
        if not "%%~nxF"=="VideoProcessingCore.dll" if not "%%~nxF"=="ai.dll" if not "%%~nxF"=="core.dll" if not "%%~nxF"=="tracking.dll" if not "%%~nxF"=="utils.dll" (
            echo Copying %%~nxF...
            copy /Y "%%F" "%DEST_DIR%"
        )
    )
)

echo All DLLs copied successfully!
