#include "ai/plugins/ui/plugin_management_widget.h"

#include <QFileDialog>
#include <QMessageBox>

#include "ai/ai_processor.h"
#include "ai/plugins/plugin_manager.h"
#include "ai/plugins/python_script_plugin.h"


namespace ai {
namespace plugins {
namespace ui {

PluginManagementWidget::PluginManagementWidget(std::shared_ptr<ai::plugins::PluginManager> plugin_manager, QWidget* parent)
    : QWidget(parent), plugin_manager_(plugin_manager)
{
    setupUI();
    refreshPluginList();
}

void PluginManagementWidget::setupUI() {
    auto layout = new QHBoxLayout(this);

    // 左侧插件列表
    auto leftWidget = new QWidget(this);
    auto leftLayout = new QVBoxLayout(leftWidget);

    pluginTree = new QTreeWidget(this);
    pluginTree->setHeaderLabels({tr("插件名称"), tr("类型"), tr("状态")});
    pluginTree->setColumnWidth(0, 200);
    pluginTree->setColumnWidth(1, 100);

    addPythonPluginButton = new QPushButton(tr("添加Python插件"), this);
    addDllPluginButton = new QPushButton(tr("添加DLL插件"), this);
    enableButton = new QPushButton(tr("启用"), this);
    enableButton->setEnabled(false);

    leftLayout->addWidget(pluginTree);
    leftLayout->addWidget(addPythonPluginButton);
    leftLayout->addWidget(addDllPluginButton);
    leftLayout->addWidget(enableButton);

    // 右侧配置栈
    configStack = new QStackedWidget(this);

    // 设置布局比例
    layout->addWidget(leftWidget, 1);
    layout->addWidget(configStack, 2);

    // 连接信号
    connect(pluginTree, &QTreeWidget::currentItemChanged,
            this, &PluginManagementWidget::onPluginSelected);
    connect(enableButton, &QPushButton::clicked,
            this, &PluginManagementWidget::onEnableButtonClicked);
    connect(addPythonPluginButton, &QPushButton::clicked,
            this, &PluginManagementWidget::onAddPythonPluginClicked);
    connect(addDllPluginButton, &QPushButton::clicked,
            this, &PluginManagementWidget::onAddDllPluginClicked);
}

void PluginManagementWidget::refreshPluginList() {
    pluginTree->clear();
    while (configStack->count() > 0) {
        QWidget* widget = configStack->widget(0);
        configStack->removeWidget(widget);
        delete widget;
    }

    if (!plugin_manager_) {
        return;
    }

    auto plugins = plugin_manager_->get_all_plugins();

    for (const auto& plugin : plugins) {
        addPluginToTree(plugin);
    }
}

void PluginManagementWidget::addPluginToTree(const std::shared_ptr<ai::plugins::TaskPlugin>& plugin) {
    auto item = new QTreeWidgetItem(pluginTree);
    item->setText(0, QString::fromStdString(plugin->get_display_name()));
    item->setText(1, QString::fromStdString(plugin->get_type()));
    item->setText(2, plugin->is_enabled() ? tr("已启用") : tr("已禁用"));

    // 存储插件指针的地址作为整数
    quintptr pluginPtr = reinterpret_cast<quintptr>(plugin.get());
    item->setData(0, Qt::UserRole, QVariant::fromValue(pluginPtr));
}

void PluginManagementWidget::onPluginSelected(QTreeWidgetItem* current, QTreeWidgetItem* previous) {
    if (!current) {
        currentPlugin = nullptr;
        enableButton->setEnabled(false);
        return;
    }

    // 从整数恢复插件指针
    quintptr pluginPtr = current->data(0, Qt::UserRole).value<quintptr>();

    // 在插件列表中查找对应的插件
    auto plugins = plugin_manager_->get_all_plugins();
    for (const auto& plugin : plugins) {
        if (reinterpret_cast<quintptr>(plugin.get()) == pluginPtr) {
            currentPlugin = plugin;
            break;
        }
    }
    enableButton->setEnabled(true);
    enableButton->setText(currentPlugin->is_enabled() ? tr("禁用") : tr("启用"));

    // 切换到对应的配置界面
    for (int i = 0; i < configStack->count(); ++i) {
        quintptr widgetPluginPtr = configStack->widget(i)->property("plugin_ptr").value<quintptr>();
        if (widgetPluginPtr == reinterpret_cast<quintptr>(currentPlugin.get())) {
            configStack->setCurrentIndex(i);
            break;
        }
    }
}

void PluginManagementWidget::onEnableButtonClicked() {
    if (!currentPlugin || !plugin_manager_) return;

    if (currentPlugin->is_enabled()) {
        plugin_manager_->disable_plugin(currentPlugin->get_name());
    } else {
        plugin_manager_->enable_plugin(currentPlugin->get_name());
    }

    // 刷新显示
    refreshPluginList();
}

void PluginManagementWidget::onAddPythonPluginClicked() {
    if (!plugin_manager_) {
        QMessageBox::warning(this, tr("错误"), tr("插件管理器未初始化"));
        return;
    }

    QString filePath = QFileDialog::getOpenFileName(
        this,
        tr("选择Python脚本"),
        QString(),
        tr("Python脚本 (*.py)")
    );

    if (filePath.isEmpty()) return;

    // Fix: Pass the script path to the constructor
    auto pythonPlugin = std::make_shared<ai::plugins::PythonScriptPlugin>(filePath.toStdString());

    if (!pythonPlugin->initialize()) {
        QMessageBox::warning(this, tr("错误"),
            tr("加载Python脚本失败：%1").arg(
                QString::fromStdString(pythonPlugin->get_error_message())));
        return;
    }

    if (!plugin_manager_->register_plugin(pythonPlugin)) {
        QMessageBox::warning(this, tr("错误"), tr("注册插件失败"));
        return;
    }

    refreshPluginList();
}

void PluginManagementWidget::onAddDllPluginClicked() {
    if (!plugin_manager_) {
        QMessageBox::warning(this, tr("错误"), tr("插件管理器未初始化"));
        return;
    }

    // 选择DLL文件
    QString filePath = QFileDialog::getOpenFileName(
        this,
        tr("选择DLL插件"),
        QString(),
        tr("DLL插件 (*.dll)")
    );

    if (filePath.isEmpty()) return;

    // 加载DLL插件
    try {
        // 加载DLL插件
        bool success = plugin_manager_->load_dll_plugin(filePath.toStdString());

        if (!success) {
            QMessageBox::warning(this, tr("错误"), tr("加载DLL插件失败"));
            return;
        }

        // 刷新插件列表
        refreshPluginList();

        QMessageBox::information(this, tr("成功"), tr("DLL插件加载成功"));
    } catch (const std::exception& e) {
        QMessageBox::warning(this, tr("错误"),
            tr("加载DLL插件时出错：%1").arg(e.what()));
    }
}

} // namespace ui
} // namespace plugins
} // namespace ai
