# -*- coding: utf-8 -*-
"""
示例计数插件脚本：计算目标数量

此插件用于计算穿过计数线的目标数量，支持按类别统计。

参数：
- count_line_y: 垂直计数线位置，范围0-1，表示屏幕高度的比例，默认为0.5
- count_line_x: 水平计数线位置，范围0-1，表示屏幕宽度的比例，默认为0.5
- required_frames: 需要处理的帧数，默认为1
"""

# No imports needed for the new implementation

# 全局变量
params = {}
frame_count = 0
total_count = 0
class_counts = {}
track_history_y = {}  # 垂直跟踪历史，用于记录已计数的轨迹
track_history_x = {}  # 水平跟踪历史，用于记录已计数的轨迹
required_frames = 1  # 默认只需要当前帧

# 定义默认参数及其描述
DEFAULT_PARAMS = {
    "count_line_y": {
        "default": 0.5,
        "description": "垂直计数线位置，范围0-1，表示屏幕高度的比例"
    },
    "count_line_x": {
        "default": 0.5,
        "description": "水平计数线位置，范围0-1，表示屏幕宽度的比例"
    },
    "required_frames": {
        "default": 1,
        "description": "需要处理的帧数，范围1-100"
    }
}

# 也可以单独定义参数描述
PARAM_DESCRIPTIONS = {
    "custom_param": "这是一个自定义参数，没有默认值"
}

def initialize(parameters):
    """初始化函数"""
    global params, frame_count, total_count, class_counts, track_history_y, track_history_x, required_frames
    params = parameters
    frame_count = 0
    total_count = 0
    class_counts = {}
    track_history_y = {}
    track_history_x = {}

    # 从参数中读取需要的帧数（如果有）
    if 'required_frames' in parameters:
        try:
            required_frames = int(parameters['required_frames'])
            # 确保帧数在合理范围内
            if required_frames < 1:
                required_frames = 1
            elif required_frames > 100:
                required_frames = 100
        except:
            required_frames = 1  # 如果转换失败，使用默认值

    # 从参数中读取垂直计数线位置
    if 'count_line_y' in parameters:
        try:
            params['count_line_y'] = float(parameters['count_line_y'])
        except:
            params['count_line_y'] = 0.5  # 默认在画面中间
    else:
        params['count_line_y'] = 0.5

    # 从参数中读取水平计数线位置
    if 'count_line_x' in parameters:
        try:
            params['count_line_x'] = float(parameters['count_line_x'])
        except:
            params['count_line_x'] = 0.5  # 默认在画面中间
    else:
        params['count_line_x'] = 0.5

    print(f"计数插件初始化，需要帧数: {required_frames}，参数:", params)

def get_required_frames():
    """返回插件需要处理的帧数"""
    global required_frames
    return required_frames

def reset():
    """重置函数"""
    global frame_count, total_count, class_counts, track_history_y, track_history_x
    frame_count = 0
    total_count = 0
    class_counts = {}
    track_history_y = {}
    track_history_x = {}
    print("计数插件已重置")

def get_info():
    """返回插件信息"""
    return {
        "name": "计数插件",
        "type": "counting",
        "description": "计算穿过计数线的目标数量",
        "version": "1.0.0",
        "author": "阿丘科技"
    }

def process(frames, tracks_list):
    """处理函数，处理多帧图像和对应的跟踪结果"""
    global frame_count, total_count, class_counts, track_history_y, track_history_x, params

    # 打印收到的帧数
    print(f"收到 {len(frames)} 帧进行处理")

    # 只处理当前帧（第一帧）
    if len(frames) > 0 and len(tracks_list) > 0:
        frame = frames[0]
        tracks = tracks_list[0]

        # 更新帧计数
        frame_count += 1

        # 获取垂直计数线位置
        count_line_y = int(params['count_line_y'] * frame.shape[0])

        # 获取水平计数线位置
        count_line_x = int(params['count_line_x'] * frame.shape[1])

        # 处理当前帧的跟踪结果
        for track in tracks:
            track_id = track["track_id"]
            cls = track["detect_class"]
            tlwh = track["tlwh"]

            # 获取目标中心点
            x = tlwh["x"] + tlwh["width"] / 2
            y = tlwh["y"] + tlwh["height"] / 2

            # 检查是否穿过垂直计数线
            if track_id in track_history_y:
                prev_y = track_history_y[track_id]
                # 如果之前在线上方，现在在线下方，或者之前在线下方，现在在线上方
                if (prev_y < count_line_y and y >= count_line_y) or (prev_y >= count_line_y and y < count_line_y):
                    # 增加计数
                    total_count += 1

                    # 更新类别计数
                    if cls not in class_counts:
                        class_counts[cls] = 0
                    class_counts[cls] += 1

            # 检查是否穿过水平计数线
            if track_id in track_history_x:
                prev_x = track_history_x[track_id]
                # 如果之前在线左侧，现在在线右侧，或者之前在线右侧，现在在线左侧
                if (prev_x < count_line_x and x >= count_line_x) or (prev_x >= count_line_x and x < count_line_x):
                    # 增加计数
                    total_count += 1

                    # 更新类别计数
                    if cls not in class_counts:
                        class_counts[cls] = 0
                    class_counts[cls] += 1

            # 更新跟踪历史
            track_history_y[track_id] = y
            track_history_x[track_id] = x

        # 准备渲染数据，而不是直接渲染图像
        # 创建渲染信息字典
        render_info = {
            "should_render": True,
            "frame_info": {
                "frame_count": frame_count,
                "object_count": total_count
            },
            "status": {
                "total_objects": total_count,
                "status_text": f"总计数: {total_count}",
                "is_error": False
            },
            "tracks": [],
            "custom_elements": [
                {
                    "type": "line",
                    "points": [[0, count_line_y], [frame.shape[1], count_line_y]],
                    "color": [0, 255, 255],  # BGR格式，黄色
                    "thickness": 2
                },
                {
                    "type": "line",
                    "points": [[count_line_x, 0], [count_line_x, frame.shape[0]]],
                    "color": [255, 0, 255],  # BGR格式，紫色
                    "thickness": 2
                }
            ]
        }

        # 添加类别计数信息
        class_info = ""
        for cls, count in class_counts.items():
            if class_info:
                class_info += ", "
            class_info += f"{cls}: {count}"

        if class_info:
            render_info["status"]["class_info"] = class_info

        # 添加跟踪目标信息
        for track in tracks:
            track_id = track["track_id"]
            tlwh = track["tlwh"]
            cls = track["detect_class"]
            x, y, w, h = int(tlwh["x"]), int(tlwh["y"]), int(tlwh["width"]), int(tlwh["height"])

            render_info["tracks"].append({
                "track_id": track_id,
                "class": cls,
                "bbox": [x, y, w, h]  # [x, y, width, height]
            })

        # 为了兼容性，仍然准备一个原始帧
        img = frame.copy()

        # 创建结果字典
        result = {
            "task_type": "counting",
            "frame_id": frame_count,
            "total_count": total_count,
            "class_counts": class_counts,
            "ext_info": {
                "count_line_y": params['count_line_y'],
                "count_line_x": params['count_line_x'],
                "frames_received": len(frames),
                "frames_requested": required_frames,
                "history_frames_available": len(frames) - 1,
                # 添加渲染信息字段，用于MainWindow渲染
                "render_info": render_info
            },
            "processed_frame": img  # 返回原始帧，不再直接绘制
        }

        # 为了兼容旧版本，将结果放在列表中返回，但C++端只会使用第一个元素
        return [result]
    else:
        # 如果没有帧或跟踪结果，返回空结果
        return [{"task_type": "counting", "frame_id": frame_count, "error": "No frames or tracks"}]
