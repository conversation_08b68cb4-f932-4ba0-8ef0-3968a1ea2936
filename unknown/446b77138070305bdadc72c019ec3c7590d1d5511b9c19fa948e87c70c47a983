# -*- coding: utf-8 -*-
"""
参数演示插件：展示参数配置功能

此插件演示了如何定义参数及其默认值和描述，以便在UI中显示。

参数：
- threshold: 阈值，用于过滤检测结果，默认为0.5
- count_line_y: 计数线位置，范围0-1，默认为0.5
- required_frames: 需要处理的帧数，默认为1
- mode: 处理模式，可选值为'simple'或'advanced'，默认为'simple'
- show_debug_info: 是否显示调试信息，默认为false
"""

import numpy as np
import cv2

# 全局变量
params = {}
frame_count = 0
total_count = 0
class_counts = {}
track_history = {}  # 跟踪历史，用于记录已计数的轨迹
required_frames = 1  # 默认只需要当前帧

# 定义默认参数及其描述
DEFAULT_PARAMS = {
    "threshold": {
        "default": 0.5,
        "description": "阈值，用于过滤检测结果，范围0-1"
    },
    "count_line_y": {
        "default": 0.5,
        "description": "计数线位置，范围0-1，表示屏幕高度的比例"
    },
    "required_frames": {
        "default": 1,
        "description": "需要处理的帧数，范围1-100"
    },
    "mode": {
        "default": "simple",
        "description": "处理模式，可选值为'simple'或'advanced'"
    },
    "show_debug_info": {
        "default": False,
        "description": "是否显示调试信息"
    }
}

# 也可以单独定义参数描述
PARAM_DESCRIPTIONS = {
    "custom_param": "这是一个自定义参数，没有默认值"
}

def initialize(parameters):
    """初始化函数"""
    global params, frame_count, total_count, class_counts, track_history, required_frames
    params = parameters
    frame_count = 0
    total_count = 0
    class_counts = {}
    track_history = {}

    # 从参数中读取需要的帧数（如果有）
    if 'required_frames' in parameters:
        try:
            required_frames = int(parameters['required_frames'])
            # 确保帧数在合理范围内
            if required_frames < 1:
                required_frames = 1
            elif required_frames > 100:
                required_frames = 100
        except:
            required_frames = 1  # 如果转换失败，使用默认值

    # 从参数中读取计数线位置
    if 'count_line_y' in parameters:
        try:
            params['count_line_y'] = float(parameters['count_line_y'])
        except:
            params['count_line_y'] = 0.5  # 默认在画面中间
    else:
        params['count_line_y'] = 0.5

    # 从参数中读取阈值
    if 'threshold' in parameters:
        try:
            params['threshold'] = float(parameters['threshold'])
        except:
            params['threshold'] = 0.5  # 默认阈值

    # 从参数中读取模式
    if 'mode' in parameters:
        params['mode'] = parameters['mode']
    else:
        params['mode'] = 'simple'  # 默认模式

    # 从参数中读取是否显示调试信息
    if 'show_debug_info' in parameters:
        params['show_debug_info'] = parameters['show_debug_info'].lower() == 'true'
    else:
        params['show_debug_info'] = False  # 默认不显示

    print(f"参数演示插件初始化，需要帧数: {required_frames}，参数:", params)

def get_required_frames():
    """返回插件需要处理的帧数"""
    global required_frames
    return required_frames

def reset():
    """重置函数"""
    global frame_count, total_count, class_counts, track_history
    frame_count = 0
    total_count = 0
    class_counts = {}
    track_history = {}
    print("参数演示插件已重置")

def get_info():
    """返回插件信息"""
    return {
        "name": "参数演示插件",
        "type": "param_demo",
        "description": "演示参数配置功能的插件",
        "version": "1.0.0",
        "author": "阿丘科技"
    }

def process(frames, tracks_list):
    """处理函数，处理多帧图像和对应的跟踪结果"""
    global frame_count, total_count, class_counts, track_history, params

    # 打印收到的帧数
    print(f"收到 {len(frames)} 帧进行处理")

    # 只处理当前帧（第一帧）
    if len(frames) > 0 and len(tracks_list) > 0:
        frame = frames[0]
        tracks = tracks_list[0]

        # 更新帧计数
        frame_count += 1

        # 获取计数线位置
        count_line_y = int(float(params['count_line_y']) * frame.shape[0])
        
        # 获取阈值
        threshold = float(params.get('threshold', '0.5'))
        
        # 获取模式
        mode = params.get('mode', 'simple')
        
        # 获取是否显示调试信息
        show_debug_info = params.get('show_debug_info', 'false').lower() == 'true'

        # 处理当前帧的跟踪结果
        for track in tracks:
            track_id = track["track_id"]
            cls = track["detect_class"]
            score = track["score"]
            tlwh = track["tlwh"]
            
            # 使用阈值过滤低置信度目标
            if score < threshold:
                continue

            # 获取目标中心点
            x = tlwh["x"] + tlwh["width"] / 2
            y = tlwh["y"] + tlwh["height"] / 2

            # 检查是否穿过计数线
            if track_id in track_history:
                prev_y = track_history[track_id]
                # 如果之前在线上方，现在在线下方，或者之前在线下方，现在在线上方
                if (prev_y < count_line_y and y >= count_line_y) or (prev_y >= count_line_y and y < count_line_y):
                    # 增加计数
                    total_count += 1

                    # 更新类别计数
                    if cls not in class_counts:
                        class_counts[cls] = 0
                    class_counts[cls] += 1

            # 更新跟踪历史
            track_history[track_id] = y

        # 准备渲染数据
        render_info = {
            "should_render": True,
            "frame_info": {
                "frame_count": frame_count,
                "object_count": total_count,
                "mode": mode,
                "threshold": threshold
            },
            "status": {
                "total_objects": total_count,
                "status_text": f"总计数: {total_count}",
                "is_error": False
            },
            "tracks": [],
            "custom_elements": [
                {
                    "type": "line",
                    "points": [[0, count_line_y], [frame.shape[1], count_line_y]],
                    "color": [0, 255, 255],  # BGR格式，黄色
                    "thickness": 2
                }
            ]
        }

        # 如果启用了调试信息，添加更多信息
        if show_debug_info:
            render_info["status"]["debug_info"] = f"模式: {mode}, 阈值: {threshold:.2f}"
            render_info["custom_elements"].append({
                "type": "text",
                "text": f"Debug: 帧 {frame_count}, 模式 {mode}",
                "position": [10, 30],
                "color": [0, 255, 0],  # BGR格式，绿色
                "font_scale": 0.8,
                "thickness": 2
            })

        # 添加类别计数信息
        class_info = ""
        for cls, count in class_counts.items():
            if class_info:
                class_info += ", "
            class_info += f"{cls}: {count}"

        if class_info:
            render_info["status"]["class_info"] = class_info

        # 添加跟踪目标信息
        for track in tracks:
            track_id = track["track_id"]
            score = track["score"]
            
            # 使用阈值过滤低置信度目标
            if score < threshold:
                continue
                
            tlwh = track["tlwh"]
            cls = track["detect_class"]
            x, y, w, h = int(tlwh["x"]), int(tlwh["y"]), int(tlwh["width"]), int(tlwh["height"])

            render_info["tracks"].append({
                "track_id": track_id,
                "class": cls,
                "score": score,
                "bbox": [x, y, w, h]  # [x, y, width, height]
            })

        # 为了兼容性，仍然准备一个原始帧
        img = frame.copy()

        # 创建结果字典
        result = {
            "task_type": "param_demo",
            "frame_id": frame_count,
            "total_count": total_count,
            "class_counts": class_counts,
            "ext_info": {
                "count_line_y": params['count_line_y'],
                "threshold": params.get('threshold', '0.5'),
                "mode": params.get('mode', 'simple'),
                "show_debug_info": params.get('show_debug_info', 'false'),
                "frames_received": len(frames),
                "frames_requested": required_frames,
                "history_frames_available": len(frames) - 1,
                # 添加渲染信息字段，用于MainWindow渲染
                "render_info": render_info
            },
            "processed_frame": img  # 返回原始帧，不再直接绘制
        }

        # 为了兼容旧版本，将结果放在列表中返回，但C++端只会使用第一个元素
        return [result]
    else:
        # 如果没有帧或跟踪结果，返回空结果
        return [{"task_type": "param_demo", "frame_id": frame_count, "error": "No frames or tracks"}]
