# -*- coding: utf-8 -*-
"""
行为检索插件：提取目标类别名，按帧顺序排列，搜索指定步骤模式

此插件用于检测装配流程中的步骤顺序，并显示当前执行的步骤和状态。

参数：
- render_interval: 渲染间隔，每隔多少帧渲染一次图像，默认为1（每帧都渲染）
- debug_mode: 是否启用调试模式，设置为 true/1/yes/y/on 则启用，否则禁用
- step_patterns: 步骤模式定义，使用逗号分隔的字符串列表
- step_sequence: 步骤顺序定义，使用逗号分隔的字符串列表
- max_history_length: 历史记录最大长度，默认为100000
"""

import numpy as np
import cv2
import re
import os

# 全局变量
params = {}
frame_count = 0
required_frames = 1  # 默认只需要当前帧
render_interval = 1  # 默认每帧都渲染
last_render_frame = 0  # 上次渲染的帧
debug_mode = False  # 是否打印调试信息

# 定义默认参数及其描述
DEFAULT_PARAMS = {
    "render_interval": {
        "default": 1,
        "description": "渲染间隔，每隔多少帧渲染一次图像"
    },
    "debug_mode": {
        "default": False,
        "description": "是否启用调试模式，设置为 true/1/yes/y/on 则启用，否则禁用"
    },
    "max_history_length": {
        "default": 100000,
        "description": "历史记录最大长度"
    }
}

# 也可以单独定义参数描述
PARAM_DESCRIPTIONS = {
    "step_patterns": "步骤模式定义，使用逗号分隔的字符串列表，例如：安装背板,安装主板,拧紧螺丝",
    "step_sequence": "步骤顺序定义，使用逗号分隔的字符串列表，例如：步骤1,步骤2,步骤3"
}

# 存储类别序列的历史记录
category_history = []
# 最大历史记录长度
max_history_length = 100000
# 当前检测到的步骤
current_step = ""
# 当前状态消息
current_status = ""
# 已完成的合规步骤列表
completed_steps = []
# 步骤模式定义
step_patterns = {
    "步骤1": r"安装背板",
    "步骤2": r"安装主板",
    "步骤3": r"拧紧螺丝"
}

# 步骤计数器，记录每个步骤出现的次数
step_counts = {}

# 当前正在执行的步骤
current_executing_step = ""
# 步骤顺序
step_sequence = ["步骤1", "步骤2", "步骤3"]
# 上一个检测到的步骤
last_detected_step = ""

# 字体对象
font = None
# 字体路径
font_path = "C:/Windows/Fonts/simhei.ttf"  # 默认使用黑体

def initialize(parameters):
    """初始化函数"""
    global params, frame_count, category_history, current_step, current_status, completed_steps
    global step_patterns, step_sequence, last_detected_step, font, step_counts, current_executing_step
    global render_interval, last_render_frame, debug_mode

    params = parameters
    frame_count = 0
    category_history = []
    current_step = ""
    current_status = ""
    last_detected_step = ""
    completed_steps = []
    step_counts = {}
    current_executing_step = ""
    last_render_frame = 0

    # 从参数中读取调试模式设置（如果有）
    if 'debug_mode' in parameters:
        debug_value = parameters['debug_mode'].lower()
        debug_mode = debug_value in ['true', '1', 'yes', 'y', 'on']
        if debug_mode:
            print(f"已启用调试模式")

    # 从参数中读取渲染间隔（如果有）
    if 'render_interval' in parameters:
        try:
            render_interval = int(parameters['render_interval'])
            if debug_mode:
                print(f"设置渲染间隔为每 {render_interval} 帧")
        except:
            # 如果转换失败，使用默认值
            render_interval = 1
            if debug_mode:
                print(f"渲染间隔参数无效，使用默认值: 每 {render_interval} 帧")

    # 从参数中读取步骤模式（如果有）
    if 'step_patterns' in parameters:
        try:
            patterns = parameters['step_patterns'].split(',')
            step_patterns = {}
            for i, pattern in enumerate(patterns):
                step_patterns[f"步骤{i+1}"] = pattern.strip()
        except:
            # 如果解析失败，使用默认值
            pass

    # 从参数中读取步骤顺序（如果有）
    if 'step_sequence' in parameters:
        try:
            step_sequence = parameters['step_sequence'].split(',')
        except:
            # 如果解析失败，使用默认值
            step_sequence = list(step_patterns.keys())

    # 从参数中读取历史记录长度（如果有）
    if 'max_history_length' in parameters:
        try:
            global max_history_length
            max_history_length = int(parameters['max_history_length'])
        except:
            # 如果转换失败，使用默认值
            pass


    print(f"行为检索插件初始化，步骤模式: {step_patterns}，步骤顺序: {step_sequence}，字体路径: {font_path}")

def get_required_frames():
    """返回插件需要处理的帧数"""
    global required_frames
    return required_frames

def reset():
    """重置函数"""
    global frame_count, category_history, current_step, current_status, last_detected_step, completed_steps, step_counts, current_executing_step, last_render_frame, debug_mode
    frame_count = 0
    category_history = []
    current_step = ""
    current_status = ""
    last_detected_step = ""
    completed_steps = []
    step_counts = {}
    current_executing_step = ""
    last_render_frame = 0
    if debug_mode:
        print("行为检索插件已重置")



def get_info():
    """返回插件信息"""
    return {
        "name": "行为监控插件",
        "type": "behavior_retrieval",
        "description": "提取目标类别名，按帧顺序排列，搜索指定步骤模式",
        "version": "2.0.0",  # 更新版本号，表示重构了渲染机制
        "author": "阿丘科技",
        "parameters": {
            "render_interval": "渲染间隔，每隔多少帧渲染一次图像，默认为1（每帧都渲染）",
            "debug_mode": "是否启用调试模式，设置为 true/1/yes/y/on 则启用，否则禁用"
        }
    }

def process(frames, tracks_list):
    """处理函数，处理多帧图像和对应的跟踪结果"""
    global frame_count, category_history, current_step, current_status, last_detected_step, completed_steps
    global step_patterns, step_sequence, max_history_length, step_counts, current_executing_step
    global render_interval, last_render_frame, debug_mode

    # 只处理当前帧（第一帧）
    if len(frames) > 0 and len(tracks_list) > 0:
        frame = frames[0]
        tracks = tracks_list[0]

        # 更新帧计数
        frame_count += 1

        # 判断是否需要渲染当前帧
        should_render = (frame_count - last_render_frame) >= render_interval

        # 提取当前帧中所有目标的类别名
        current_categories = []
        for track in tracks:
            if track["state"] == 1 or track["state"] == 2:  # 只处理确认的跟踪目标
                category = track["detect_class"]
                if category not in current_categories:
                    current_categories.append(category)

        # 将当前帧的类别名添加到历史记录
        if current_categories:
            category_str = "+".join(sorted(current_categories))
            category_history.append(category_str)

            # 限制历史记录长度
            if len(category_history) > max_history_length:
                category_history = category_history[-max_history_length:]

        # 分析当前帧中的类别
        if current_categories:
            # 更新步骤计数器
            for category in current_categories:
                # 查找该类别对应的步骤
                for step, pattern in step_patterns.items():
                    if pattern in category:
                        if step not in step_counts:
                            step_counts[step] = 0
                        step_counts[step] += 1

            # 确定当前正在执行的步骤
            # 首先检查是否有新的步骤出现
            new_step_detected = False
            for step in step_sequence:
                if step in step_counts and step_counts[step] >= 5:  # 需要至少出现5次才认为是有效步骤
                    if current_executing_step != step and (current_executing_step == "" or
                                                          step_sequence.index(step) == step_sequence.index(current_executing_step) + 1):
                        current_executing_step = step
                        new_step_detected = True
                        break

            # 如果没有检测到新步骤，但有当前执行的步骤，检查是否有错误的步骤顺序
            if not new_step_detected and current_executing_step:
                current_index = step_sequence.index(current_executing_step)
                # 检查是否有后续步骤出现但顺序错误
                for step in step_sequence:
                    if step in step_counts and step_counts[step] >= 5:
                        step_index = step_sequence.index(step)
                        if step_index > current_index + 1:  # 跳过了中间步骤
                            current_status = f"步骤错误：应先执行{step_sequence[current_index+1]}，但检测到{step}"
                            break
                        elif step_index < current_index:  # 返回到之前的步骤
                            # 这是允许的，可能是重复执行某个步骤
                            pass

            # 更新当前步骤和状态
            if current_executing_step:
                current_step = current_executing_step

                # 如果没有错误状态，设置正常状态
                if "错误" not in current_status:
                    current_status = f"正在执行：{step_patterns[current_step]}"

                # 更新已完成步骤列表
                expected_steps = step_sequence[:step_sequence.index(current_step)+1]
                for step in expected_steps:
                    if step in step_counts and step_counts[step] >= 5 and step not in completed_steps:
                        completed_steps.append(step)

            # 打印调试信息
            if debug_mode:
                print(f"当前类别: {current_categories}, 步骤计数: {step_counts}, 当前执行步骤: {current_executing_step}, 状态: {current_status}")

        # 更新渲染帧计数
        if should_render:
            last_render_frame = frame_count

        # 准备渲染数据，而不是直接渲染图像
        # 创建渲染信息字典
        render_info = {
            "should_render": should_render,
            "frame_info": {
                "frame_count": frame_count,
                "category_text": "+".join(current_categories) if current_categories else "无"
            },
            "status": {
                "current_step": "",
                "step_progress": "",
                "status_text": current_status,
                "is_error": "错误" in current_status if current_status else False
            },
            "completed_steps": [],
            "tracks": []
        }

        # 添加当前步骤信息
        if current_step:
            step_index = step_sequence.index(current_step)
            total_steps = len(step_sequence)
            render_info["status"]["current_step"] = step_patterns[current_step]
            render_info["status"]["step_progress"] = f"{step_index+1}/{total_steps}"

        # 添加已完成步骤信息
        if completed_steps:
            for step in completed_steps:
                render_info["completed_steps"].append({
                    "step_name": step_patterns[step],
                    "step_id": step
                })

        # 添加轨迹信息
        for track in tracks:
            if track["state"] == 1 or track["state"] == 2:  # 只处理确认的跟踪目标
                track_id = track["track_id"]
                tlwh = track["tlwh"]
                cls = track["detect_class"]
                x, y, w, h = int(tlwh["x"]), int(tlwh["y"]), int(tlwh["width"]), int(tlwh["height"])

                render_info["tracks"].append({
                    "track_id": track_id,
                    "class": cls,
                    "bbox": [x, y, w, h]  # [x, y, width, height]
                })

        # 为了兼容性，仍然准备一个原始帧
        img = frame.copy()

        # 创建结果字典
        result = {
            "task_type": "behavior_retrieval",
            "frame_id": frame_count,
            "ext_info": {
                "current_categories": current_categories,
                "category_history": category_history[-10:],  # 只返回最近10个记录
                "current_step": step_patterns[current_step] if current_step else "",
                "current_status": current_status,
                "completed_steps": [step_patterns[step] for step in completed_steps],
                "step_counts": step_counts,
                "frames_received": len(frames),
                "render_interval": render_interval,
                "last_render_frame": last_render_frame,
                "debug_mode": debug_mode,
                # 添加渲染信息字段，用于MainWindow渲染
                "render_info": render_info
            },
            "processed_frame": img  # 返回原始帧，不再直接绘制
        }

        # 为了兼容旧版本，将结果放在列表中返回，但C++端只会使用第一个元素
        return [result]
    else:
        # 如果没有帧或跟踪结果，返回空结果
        return [{"task_type": "behavior_retrieval", "frame_id": frame_count, "error": "No frames or tracks"}]
