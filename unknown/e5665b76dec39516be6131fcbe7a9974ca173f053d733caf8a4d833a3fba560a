﻿#include "ui/model_settings_dialog.h"

#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QSpinBox>
#include <QVBoxLayout>

namespace ui {

ModelSettingsDialog::ModelSettingsDialog(const QString& defaultPrompt,
                                       double defaultScore,
                                       int defaultIou,
                                       QWidget* parent)
    : QDialog(parent) {
    setWindowTitle(tr("设置万物检测大模型参数"));
    setMinimumWidth(450);

    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 提示词设置
    QHBoxLayout* promptLayout = new QHBoxLayout();
    QLabel* promptLabel = new QLabel(tr("检测提示词:"), this);
    promptEdit = new QLineEdit(defaultPrompt, this);
    promptLabel->setBuddy(promptEdit);
    promptLayout->addWidget(promptLabel);
    promptLayout->addWidget(promptEdit);

    // 添加提示说明
    QLabel* promptHint = new QLabel(tr("提示：使用逗号分隔多个物体，例如 fire, person, vehicle"), this);
    promptHint->setProperty("class", "hint");

    // 置信度阈值设置
    QHBoxLayout* scoreLayout = new QHBoxLayout();
    QLabel* scoreLabel = new QLabel(tr("置信度阈值:"), this);
    scoreSpinBox = new QDoubleSpinBox(this);
    scoreSpinBox->setRange(0.0, 1.0);
    scoreSpinBox->setValue(defaultScore);
    scoreSpinBox->setSingleStep(0.05);
    scoreSpinBox->setDecimals(2);
    scoreLabel->setBuddy(scoreSpinBox);
    scoreLayout->addWidget(scoreLabel);
    scoreLayout->addWidget(scoreSpinBox);

    // 添加置信度说明
    QLabel* scoreHint = new QLabel(tr("提示：值越高筛选越严格 (0.0-1.0)"), this);
    scoreHint->setProperty("class", "hint");

    // IOU阈值设置
    QHBoxLayout* iouLayout = new QHBoxLayout();
    QLabel* iouLabel = new QLabel(tr("IOU阈值:"), this);
    iouSpinBox = new QSpinBox(this);
    iouSpinBox->setRange(1, 100);
    iouSpinBox->setValue(defaultIou);
    iouSpinBox->setSingleStep(5);
    iouSpinBox->setSuffix("%");
    iouLabel->setBuddy(iouSpinBox);
    iouLayout->addWidget(iouLabel);
    iouLayout->addWidget(iouSpinBox);

    // 添加IOU说明
    QLabel* iouHint = new QLabel(tr("提示：交并比阈值，值越高重叠框过滤越严格 (1-100%)"), this);
    iouHint->setProperty("class", "hint");

    // 按钮
    QHBoxLayout* buttonsLayout = new QHBoxLayout();
    QPushButton* okButton = new QPushButton(tr("确定"), this);
    QPushButton* cancelButton = new QPushButton(tr("取消"), this);

    buttonsLayout->addStretch();
    buttonsLayout->addWidget(okButton);
    buttonsLayout->addWidget(cancelButton);

    // 添加所有组件到主布局
    mainLayout->addLayout(promptLayout);
    mainLayout->addWidget(promptHint);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(scoreLayout);
    mainLayout->addWidget(scoreHint);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(iouLayout);
    mainLayout->addWidget(iouHint);
    mainLayout->addSpacing(15);
    mainLayout->addLayout(buttonsLayout);

    // 连接信号
    connect(okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
}

QString ModelSettingsDialog::getPromptValue() const {
    return promptEdit->text();
}

double ModelSettingsDialog::getScoreValue() const {
    return scoreSpinBox->value();
}

int ModelSettingsDialog::getIouValue() const {
    return iouSpinBox->value();
}

} // namespace ui
