﻿#pragma once

#include <QKeyEvent>
#include <QMouseEvent>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QResizeEvent>
#include <QWidget>
#include <opencv2/opencv.hpp>
#include "ai/frame_result.h"

namespace ui {

/**
 * @brief 全屏视频窗口类
 */
class FullscreenVideoWindow : public QWidget {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    FullscreenVideoWindow(QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~FullscreenVideoWindow();

    /**
     * @brief 设置要显示的视频帧（兼容旧接口）
     * @param pixmap 视频帧图像
     */
    void updateFrame(const QPixmap& pixmap);

    /**
     * @brief 使用OpenCV Mat和渲染结果更新显示
     * @param frame 视频帧
     * @param result 处理结果
     */
    void updateFrameWithResult(const cv::Mat& frame, const ai::FrameResult& result);

protected:
    /**
     * @brief 处理按键事件
     * @param event 按键事件
     */
    void keyPressEvent(QKeyEvent* event) override;

    /**
     * @brief 处理鼠标双击事件
     * @param event 鼠标事件
     */
    void mouseDoubleClickEvent(QMouseEvent* event) override;

    /**
     * @brief 处理窗口大小变化事件
     * @param event 大小变化事件
     */
    void resizeEvent(QResizeEvent* event) override;

private:
    QGraphicsView* videoView;  ///< 视频显示视图
    cv::Mat currentFrame;      ///< 当前帧
    ai::FrameResult currentResult; ///< 当前处理结果
    bool hasResult;            ///< 是否有处理结果
};

} // namespace ui
