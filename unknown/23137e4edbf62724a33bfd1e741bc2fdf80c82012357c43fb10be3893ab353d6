# -*- coding: utf-8 -*-
"""
示例帧处理脚本：在模型推理前处理多帧图像

支持的处理模式：
1. average - 多帧平均
2. difference - 帧差分析
3. motion_blur - 运动模糊
4. optical_flow - 光流分析
5. background_subtraction - 背景减除
6. binary - 二值化
7. frame_diff_accumulation - 帧差累加（新增）

帧差累加模式参数：
- alpha: 叠加透明度，默认为0.7
- required_frames: 需要的帧数，至少为5
"""

import numpy as np
import cv2
import time

# 尝试导入skimage库，如果不可用则使用备用方法
try:
    from skimage.filters import threshold_otsu
    HAS_SKIMAGE = True
except ImportError:
    HAS_SKIMAGE = False
    print("警告: skimage库不可用，将使用备用方法计算阈值")

# 全局变量
params = {}
required_frames = 5  # 默认需要5帧

def initialize(parameters):
    """初始化函数"""
    global params, required_frames
    params = parameters

    # 从参数中读取需要的帧数（如果有）
    if 'required_frames' in parameters:
        try:
            required_frames = int(parameters['required_frames'])
            # 确保帧数在合理范围内
            if required_frames < 1:
                required_frames = 1
            elif required_frames > 100:
                required_frames = 100
        except:
            required_frames = 5  # 如果转换失败，使用默认值

    # 从参数中读取处理模式
    if 'mode' not in parameters:
        params['mode'] = 'average'  # 默认使用平均模式

    # 如果是帧差累加模式，设置默认参数
    # if params['mode'] == 'frame_diff_accumulation':
    if 'frame_diff' in params['mode']:
        # 设置透明度参数，默认为0.7
        if 'alpha' not in params:
            params['alpha'] = '0.7'

        # 如果是帧差累加模式，至少需要5帧
        if required_frames < 5:
            required_frames = 5
            print("帧差累加模式至少需要5帧，已自动调整")

    print(f"帧处理脚本初始化，需要帧数: {required_frames}，处理模式: {params['mode']}")

def get_required_frames():
    """返回脚本需要处理的帧数"""
    global required_frames
    return required_frames

def process_frames(frames):
    """处理多帧图像，返回一个处理后的图像"""
    global params

    # 打印收到的帧数
    # print(f"收到 {len(frames)} 帧进行处理")

    # 如果没有帧，返回空图像
    if len(frames) == 0:
        return np.zeros((100, 100, 3), dtype=np.uint8)

    # 获取处理模式
    mode = params.get('mode', 'average')

    # print("mode:", mode)

    # 根据不同模式处理帧
    if mode == 'average':
        # 平均模式：计算多帧的平均值
        result = np.zeros_like(frames[0], dtype=np.float32)
        for frame in frames:
            result += frame.astype(np.float32) / len(frames)
        return result.astype(np.uint8)

    elif mode == 'difference':
        # 差分模式：计算当前帧与前一帧的差异
        result = frames[0].copy()
        tmp_0 = cv2.cvtColor(frames[0], cv2.COLOR_BGR2GRAY)
        for i in range(1, len(frames)):
            tmp_1 = cv2.cvtColor(frames[i], cv2.COLOR_BGR2GRAY)
            diff = cv2.absdiff(tmp_0, tmp_1)
            # 增强差异
            _, thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
            # 在原图上标记差异区域
            result[thresh > 0] = [0, 0, 255]  # 红色标记差异
        return result

    elif mode == 'motion_blur':
        # 运动模糊模式
        kernel_size = int(params.get('kernel_size', '5'))
        kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)
        result = cv2.filter2D(frames[0], -1, kernel)
        return result

    elif mode == 'optical_flow':
        # 光流模式：计算光流并可视化
        frame1 = cv2.cvtColor(frames[0], cv2.COLOR_BGR2GRAY)
        frame2 = cv2.cvtColor(frames[1], cv2.COLOR_BGR2GRAY)

        # 计算光流
        flow = cv2.calcOpticalFlowFarneback(frame2, frame1, None, 0.5, 3, 15, 3, 5, 1.2, 0)

        # 将光流转换为极坐标
        mag, ang = cv2.cartToPolar(flow[..., 0], flow[..., 1])

        # 创建HSV图像
        hsv = np.zeros((frame1.shape[0], frame1.shape[1], 3), dtype=np.uint8)
        hsv[..., 0] = ang * 180 / np.pi / 2  # 色调根据方向
        hsv[..., 1] = 255  # 饱和度最大
        hsv[..., 2] = cv2.normalize(mag, None, 0, 255, cv2.NORM_MINMAX)  # 亮度根据幅度

        # 转换回BGR
        result = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

        # 叠加到原图
        alpha = float(params.get('alpha', '0.5'))
        return cv2.addWeighted(frames[0], 1-alpha, result, alpha, 0)

    elif mode == 'background_subtraction':
        # 背景减除模式
        # 创建背景减除器
        backSub = cv2.createBackgroundSubtractorMOG2()

        # 对所有帧应用背景减除
        for frame in frames[1:]:
            backSub.apply(frame)

        # 对当前帧应用背景减除
        fgMask = backSub.apply(frames[0])

        # 应用掩码
        result = frames[0].copy()
        result[fgMask == 0] = [0, 0, 0]  # 背景设为黑色

        return result
    elif mode == "binary":
        # 二值化模式
        gray = cv2.cvtColor(frames[0], cv2.COLOR_BGR2GRAY)
        _, result = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        return cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)

    elif mode == "frame_diff_accumulation":
        # 帧差累加模式
        if len(frames) < 3:
            # 至少需要3帧
            return frames[0]

        # 预处理函数，将图像转为灰度图并进行高斯模糊
        def preprocessing(image):
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            gray_image = cv2.GaussianBlur(gray_image, (5, 5), 0)
            return gray_image

        # 计算帧差函数
        def cal_frame_diff(pre_f, cur_f):
            pre_gray = preprocessing(pre_f).astype(np.int16)
            cur_gray = preprocessing(cur_f).astype(np.int16)
            diff_gray = np.abs(pre_gray - cur_gray)
            return diff_gray

        # 计算所有相邻帧的帧差
        frame_diff_list = []
        for i in range(1, len(frames)):
            pre_frame = frames[i-1]
            cur_frame = frames[i]
            frame_diff = cal_frame_diff(pre_frame, cur_frame)
            frame_diff_list.append(frame_diff)

        # 帧差累加
        diff_map = np.zeros_like(frame_diff_list[0], dtype=np.float32)
        for diff in frame_diff_list:
            diff_map += diff

        # 得到平均帧差
        diff_map = diff_map / len(frame_diff_list)

        # 计算阈值
        if HAS_SKIMAGE:
            try:
                # 尝试使用Otsu算法计算阈值
                thresh = threshold_otsu(diff_map)
            except Exception as e:
                print(f"使用Otsu算法计算阈值失败: {e}")
                # 如果失败，使用备用方法
                mean_th = np.mean(diff_map)
                thresh = mean_th * 10
        else:
            # 如果skimage不可用，使用备用方法
            # 计算平均值和标准差
            mean_th = np.mean(diff_map)
            std_th = np.std(diff_map)
            # 使用平均值加两倍标准差作为阈值
            thresh = mean_th + 2 * std_th

        # 二值化帧差图
        diff_map_bw = (diff_map > thresh).astype(np.uint8) * 255

        # 将帧差图转换为彩色图像
        # 先将diff_map转换为0-255范围的uint8类型
        diff_map_norm = cv2.normalize(diff_map, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        diff_map_color = cv2.applyColorMap(diff_map_norm, cv2.COLORMAP_JET)

        # 对二值化后的帧差图进行形态学操作，去除噪点
        kernel = np.ones((3, 3), np.uint8)
        diff_map_bw = cv2.morphologyEx(diff_map_bw, cv2.MORPH_OPEN, kernel)
        diff_map_bw = cv2.morphologyEx(diff_map_bw, cv2.MORPH_CLOSE, kernel)

        # 只保留二值化图中为255的区域的彩色图
        mask = cv2.cvtColor(diff_map_bw, cv2.COLOR_GRAY2BGR)
        diff_map_color = cv2.bitwise_and(diff_map_color, mask)
        result = diff_map_color

        # # 将帧差叠加到原始图像上
        # alpha = float(params.get('alpha', '1.0'))
        # result = cv2.addWeighted(frames[0], 1-alpha, diff_map_color, alpha, 0)

        # # 添加文本信息
        # max_diff = np.max(diff_map)
        # mean_diff = np.mean(diff_map)
        # cv2.putText(result, f"Max Diff: {max_diff:.2f}, Mean: {mean_diff:.2f}", (10, 30),
        #             cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        # cv2.putText(result, f"Threshold: {thresh:.2f}", (10, 60),
        #             cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        save_result_image = params.get('save_result_image', 'false')
        if save_result_image == 'true':
            save_path = params.get('save_path', 'C:/Users/<USER>/Downloads/test_video/extract_ufo/')
            cv2.imwrite(save_path + str(time.time())+".png", result)
        return result

    elif mode == "frame_diff_accumulation_xingpeng_v3":
        # 帧差累加模式
        if len(frames) < 3:
            # 至少需要3帧
            return frames[0]

        def preprocessing(image):
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            gray_image = cv2.GaussianBlur(gray_image, (5, 5), 0)

            return gray_image

        def cal_frame_diff(pre_f, cur_f, mode='gray'):
            pre_gray = preprocessing(pre_f).astype(np.int16)
            cur_gray = preprocessing(cur_f).astype(np.int16)

            if mode == 'gray':
                diff_gray = np.abs(pre_gray - cur_gray)

                return diff_gray
            else:
                flow = cv2.calcOpticalFlowFarneback(pre_gray, cur_gray, None,
                                                    pyr_scale=0.3, levels=3, winsize=15,
                                                    iterations=3, poly_n=5, poly_sigma=1.2,
                                                    flags=0)
                mag, ang = cv2.cartToPolar(flow[..., 0], flow[..., 1])
                flow[..., 0] += 0.001
                mag_weight = np.abs((2 / np.pi) * np.atan(flow[..., 1] / flow[..., 0]))
                mag = mag * mag_weight

                diff_gray = np.abs(pre_gray - cur_gray).astype(np.float32)
                diff_gray = diff_gray * mag_weight

                return diff_gray

        def process_frame_clip_color(f_clip):
            def colorize(bw_img):
                bw_img = bw_img / 5 * 255
                rgb_img = cv2.applyColorMap(bw_img.astype(np.uint8), cv2.COLORMAP_JET)

                return rgb_img

            diff_map = np.zeros(f_clip[0].shape, dtype=np.float32)
            for f in f_clip:
                diff_map += f

            diff_map = diff_map / 5

            mean_th = np.mean(diff_map)

            th = mean_th * 10
            if th < 1.0:
                th = 1.0

            diff_map_mask = (diff_map > th).astype(np.uint8) * 255  #

            kernel = np.ones((3, 3), np.uint8)
            diff_map_mask = cv2.morphologyEx(diff_map_mask, cv2.MORPH_OPEN, kernel)
            diff_map_mask = cv2.morphologyEx(diff_map_mask, cv2.MORPH_CLOSE, kernel)

            diff_map_bw = np.zeros(f_clip[0].shape, dtype=np.int16)
            for idx, f in enumerate(f_clip):
                f_bw = (f > th).astype(np.uint8)
                f_bw = f_bw * (idx + 1)
                diff_map_bw = np.maximum(diff_map_bw, f_bw)

            diff_map_bw = colorize(diff_map_bw)
            diff_map_bw[diff_map_mask == 0] = 0

            return diff_map_bw

        all_frames = frames
        
        total_frame_count = len(all_frames)

        frame_diff_clip = []
        for i in range(1, total_frame_count, 1):
            pre_frame = all_frames[i - 1]
            cur_frame = all_frames[i]
            frame_diff = cal_frame_diff(pre_frame, cur_frame, 'gray')
            frame_diff_clip.append(frame_diff)

        result = process_frame_clip_color(frame_diff_clip)

        save_result_image = params.get('save_result_image', 'false')
        if save_result_image == 'true':
            save_path = params.get('save_path', 'F:/XinAo_Frame_Diff/')
            cv2.imwrite(save_path + str(time.time()) + ".png", result)

        return result

    else:
        # 默认返回第一帧
        return frames[0]

