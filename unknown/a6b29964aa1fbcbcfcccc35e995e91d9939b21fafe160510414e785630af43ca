#include "ui/task_manager_dialog.h"

#include <QMessageBox>
#include <algorithm>

#include "ai/plugins/ui/plugin_config_dialog.h"

namespace ui {

TaskManagerDialog::TaskManagerDialog(std::shared_ptr<ai::AiProcessor> processor,
                                 std::shared_ptr<core::Project> project,
                                 QWidget* parent)
    : QDialog(parent), processor_(processor), project_(project) {
    initUI();
}

TaskManagerDialog::~TaskManagerDialog() {
}

void TaskManagerDialog::initUI() {
    mainLayout_ = new QVBoxLayout(this);

    // 创建标题标签
    QLabel* titleLabel = new QLabel(tr("可用任务列表"), this);
    titleLabel->setStyleSheet("font-weight: bold; font-size: 14px;");
    mainLayout_->addWidget(titleLabel);

    // 创建任务列表控件
    taskListWidget_ = new QListWidget(this);
    taskListWidget_->setSelectionMode(QAbstractItemView::SingleSelection);
    taskListWidget_->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(taskListWidget_, &QListWidget::itemDoubleClicked, this, &TaskManagerDialog::onTaskItemDoubleClicked);
    connect(taskListWidget_, &QListWidget::customContextMenuRequested, this, &TaskManagerDialog::onTaskListContextMenu);
    mainLayout_->addWidget(taskListWidget_);

    // 创建按钮布局
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    // 创建配置按钮
    configButton_ = new QPushButton(tr("配置"), this);
    connect(configButton_, &QPushButton::clicked, this, &TaskManagerDialog::configureSelectedTask);
    buttonLayout->addWidget(configButton_);

    // 创建启用/禁用按钮
    toggleButton_ = new QPushButton(tr("启用/禁用"), this);
    connect(toggleButton_, &QPushButton::clicked, this, &TaskManagerDialog::toggleSelectedTask);
    buttonLayout->addWidget(toggleButton_);

    // 添加弹簧
    buttonLayout->addStretch();

    // 创建关闭按钮
    closeButton_ = new QPushButton(tr("关闭"), this);
    connect(closeButton_, &QPushButton::clicked, this, &QDialog::accept);
    buttonLayout->addWidget(closeButton_);

    mainLayout_->addLayout(buttonLayout);

    // 创建右键菜单
    contextMenu_ = new QMenu(this);
    QAction* configAction = new QAction(tr("配置"), this);
    connect(configAction, &QAction::triggered, this, &TaskManagerDialog::configureSelectedTask);
    contextMenu_->addAction(configAction);

    QAction* toggleAction = new QAction(tr("启用/禁用"), this);
    connect(toggleAction, &QAction::triggered, this, &TaskManagerDialog::toggleSelectedTask);
    contextMenu_->addAction(toggleAction);

    setLayout(mainLayout_);
}

void TaskManagerDialog::refreshTaskList() {
    taskListWidget_->clear();

    // 获取所有插件
    auto plugins = processor_->get_all_plugins();

    // 如果有Project对象，获取启用的插件列表和插件参数
    std::vector<std::string> enabledPlugins;
    if (project_) {
        enabledPlugins = project_->get_enabled_plugins();

        // 打印启用的插件列表，用于调试
        std::cout << "TaskManagerDialog: 从工程中获取的启用插件列表: " << std::endl;
        for (const auto& pluginName : enabledPlugins) {
            std::cout << "  " << pluginName << std::endl;
        }
    }

    // 创建任务列表项
    for (const auto& plugin : plugins) {
        std::string pluginName = plugin->get_name();

        // 根据Project中的启用状态设置插件状态
        if (project_) {
            bool shouldEnable = std::find(enabledPlugins.begin(), enabledPlugins.end(), pluginName) != enabledPlugins.end();

            if (shouldEnable && !plugin->is_enabled()) {
                processor_->enable_plugin(pluginName);
            } else if (!shouldEnable && plugin->is_enabled()) {
                processor_->disable_plugin(pluginName);
            }

            // 设置插件参数
            auto params = project_->get_plugin_params(pluginName);
            if (!params.empty()) {
                plugin->set_params(params);
            }
        }

        QListWidgetItem* item = createTaskItem(plugin);
        taskListWidget_->addItem(item);
    }

    // 如果有选中项，选择第一个
    if (taskListWidget_->count() > 0 && !taskListWidget_->currentItem()) {
        taskListWidget_->setCurrentRow(0);
    }

    // 更新按钮状态
    bool hasSelection = taskListWidget_->currentItem() != nullptr;
    configButton_->setEnabled(hasSelection);
    toggleButton_->setEnabled(hasSelection);
}

QListWidgetItem* TaskManagerDialog::createTaskItem(std::shared_ptr<ai::plugins::TaskPlugin> plugin) {
    QListWidgetItem* item = new QListWidgetItem();
    updateTaskItem(item, plugin);

    // 存储插件指针的地址作为整数
    quintptr pluginPtr = reinterpret_cast<quintptr>(plugin.get());
    item->setData(Qt::UserRole, QVariant::fromValue(pluginPtr));

    // 存储插件名称，用于后续查找
    item->setData(Qt::UserRole + 1, QString::fromStdString(plugin->get_name()));

    return item;
}

void TaskManagerDialog::updateTaskItem(QListWidgetItem* item, std::shared_ptr<ai::plugins::TaskPlugin> plugin) {

    // 设置文本
    QString text = QString::fromStdString(plugin->get_display_name());
    if (!plugin->is_enabled()) {
        text += tr(" (已禁用)");
    }
    item->setText(text);

    // 设置提示
    QString tooltip = QString::fromStdString(plugin->get_description());
    tooltip += tr("\n类型: ") + QString::fromStdString(plugin->get_type());
    tooltip += tr("\n版本: ") + QString::fromStdString(plugin->get_version());
    tooltip += tr("\n作者: ") + QString::fromStdString(plugin->get_author());
    item->setToolTip(tooltip);

    // 设置字体
    QFont font = item->font();
    if (plugin->is_enabled()) {
        font.setBold(true);
    } else {
        font.setItalic(true);
    }
    item->setFont(font);
}

void TaskManagerDialog::configureSelectedTask() {
    QListWidgetItem* currentItem = taskListWidget_->currentItem();
    if (!currentItem) {
        return;
    }

    // 获取插件名称
    QString pluginNameQt = currentItem->data(Qt::UserRole + 1).toString();
    if (pluginNameQt.isEmpty()) {
        QMessageBox::warning(this, tr("错误"), tr("无效的插件"));
        return;
    }

    // 从处理器中获取插件
    std::string pluginName = pluginNameQt.toStdString();
    auto plugin = processor_->get_plugin(pluginName);
    if (!plugin) {
        QMessageBox::warning(this, tr("错误"), tr("无法找到插件：%1").arg(pluginNameQt));
        return;
    }

    // 从Project获取插件参数
    std::map<std::string, std::string> params;
    if (project_) {
        params = project_->get_plugin_params(pluginName);

        // 如果Project中有参数，设置到插件
        if (!params.empty()) {
            plugin->set_params(params);
        }
    }

    // 创建配置对话框
    ai::plugins::ui::PluginConfigDialog dialog(plugin, project_, this);
    dialog.setWindowTitle(QString::fromStdString(plugin->get_display_name()));

    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        // 获取更新后的插件参数
        params = plugin->get_params();

        // 打印调试信息
        std::cout << "TaskManagerDialog: 获取插件 " << pluginName << " 的参数：" << std::endl;
        for (const auto& param : params) {
            std::cout << "  " << param.first << ": " << param.second << std::endl;
        }

        // 保存到Project
        if (project_ && !params.empty()) {
            std::cout << "TaskManagerDialog: 保存插件 " << pluginName << " 的参数到 Project" << std::endl;
            project_->set_plugin_params(pluginName, params);

            // 验证参数是否正确保存
            auto savedParams = project_->get_plugin_params(pluginName);
            std::cout << "TaskManagerDialog: 从 Project 获取插件 " << pluginName << " 的参数：" << std::endl;
            for (const auto& param : savedParams) {
                std::cout << "  " << param.first << ": " << param.second << std::endl;
            }
        }

        // 重置插件状态
        plugin->reset();

        // 重新初始化插件
        if (processor_) {
            std::cout << "TaskManagerDialog: 重新初始化插件 " << pluginName << std::endl;

            // 获取插件参数
            auto params = plugin->get_params();

            // 重新设置参数，这会触发 load_parameters 方法
            plugin->set_params(params);

            // 打印参数，用于调试
            std::cout << "TaskManagerDialog: 插件 " << pluginName << " 的参数已重新加载：" << std::endl;
            for (const auto& param : params) {
                std::cout << "  " << param.first << ": " << param.second << std::endl;
            }

            // 如果插件已启用，立即应用参数
            if (plugin->is_enabled()) {
                std::cout << "TaskManagerDialog: 插件 " << pluginName << " 已启用，立即应用参数" << std::endl;

                // 如果插件需要处理多帧，可能需要等待足够的帧
                int requiredFrames = plugin->get_required_frames();
                std::cout << "TaskManagerDialog: 插件 " << pluginName << " 需要 " << requiredFrames << " 帧" << std::endl;
            }
        }

        // 更新任务列表项
        updateTaskItem(currentItem, plugin);
    }
}

void TaskManagerDialog::toggleSelectedTask() {
    QListWidgetItem* currentItem = taskListWidget_->currentItem();
    if (!currentItem) {
        return;
    }

    // 获取插件名称
    QString pluginNameQt = currentItem->data(Qt::UserRole + 1).toString();
    if (pluginNameQt.isEmpty()) {
        QMessageBox::warning(this, tr("错误"), tr("无效的插件"));
        return;
    }

    // 从处理器中获取插件
    std::string pluginName = pluginNameQt.toStdString();
    auto plugin = processor_->get_plugin(pluginName);
    if (!plugin) {
        QMessageBox::warning(this, tr("错误"), tr("无法找到插件：%1").arg(pluginNameQt));
        return;
    }

    // 切换插件状态
    bool enabled = plugin->is_enabled();
    if (enabled) {
        processor_->disable_plugin(pluginName);
    } else {
        processor_->enable_plugin(pluginName);
    }

    // 更新Project中的插件启用状态
    if (project_) {
        // 获取当前启用的插件列表
        std::vector<std::string> enabledPlugins = project_->get_enabled_plugins();

        std::cout << "TaskManagerDialog: 切换插件 " << pluginName << " 的状态，当前状态: "
                  << (enabled ? "启用" : "禁用") << std::endl;

        std::cout << "TaskManagerDialog: 切换前的启用插件列表: " << std::endl;
        for (const auto& name : enabledPlugins) {
            std::cout << "  " << name << std::endl;
        }

        if (!enabled) {
            // 如果插件被启用，添加到启用列表
            if (std::find(enabledPlugins.begin(), enabledPlugins.end(), pluginName) == enabledPlugins.end()) {
                enabledPlugins.push_back(pluginName);
                std::cout << "TaskManagerDialog: 添加插件 " << pluginName << " 到启用列表" << std::endl;
            }
        } else {
            // 如果插件被禁用，从启用列表中移除
            auto it = std::find(enabledPlugins.begin(), enabledPlugins.end(), pluginName);
            if (it != enabledPlugins.end()) {
                enabledPlugins.erase(it);
                std::cout << "TaskManagerDialog: 从启用列表中移除插件 " << pluginName << std::endl;
            }
        }

        // 更新Project中的启用插件列表
        project_->set_enabled_plugins(enabledPlugins);

        std::cout << "TaskManagerDialog: 切换后的启用插件列表: " << std::endl;
        for (const auto& name : enabledPlugins) {
            std::cout << "  " << name << std::endl;
        }
    }

    // 更新任务列表项
    updateTaskItem(currentItem, plugin);
}

void TaskManagerDialog::onTaskItemDoubleClicked(QListWidgetItem* item) {
    configureSelectedTask();
}

void TaskManagerDialog::onTaskListContextMenu(const QPoint& pos) {
    QListWidgetItem* item = taskListWidget_->itemAt(pos);
    if (item) {
        taskListWidget_->setCurrentItem(item);
        contextMenu_->popup(taskListWidget_->viewport()->mapToGlobal(pos));
    }
}

} // namespace ui





