﻿#include "utils/dialog_utils.h"

#include <QDialog>
#include <QFrame>
#include <QHBoxLayout>
#include <QLabel>
#include <QPixmap>
#include <QPushButton>
#include <QScrollArea>
#include <QVBoxLayout>

namespace utils {

void showScrollableMessageBox(QWidget* parent, const QString& title, 
                             const QString& text, 
                             QMessageBox::Icon icon) {
    QDialog dialog(parent);
    dialog.setWindowTitle(title);
    dialog.setMinimumSize(500, 300);
    
    // 设置窗口图标
    switch (icon) {
        case QMessageBox::Warning:
            dialog.setWindowIcon(QMessageBox::standardIcon(QMessageBox::Warning));
            break;
        case QMessageBox::Critical:
            dialog.setWindowIcon(QMessageBox::standardIcon(QMessageBox::Critical));
            break;
        case QMessageBox::Information:
            dialog.setWindowIcon(QMessageBox::standardIcon(QMessageBox::Information));
            break;
        default:
            break;
    }
    
    QVBoxLayout* layout = new QVBoxLayout(&dialog);
    
    // 创建图标和文本的水平布局
    QHBoxLayout* topLayout = new QHBoxLayout();
    
    // 添加图标标签
    QLabel* iconLabel = new QLabel();
    QPixmap iconPixmap = QMessageBox::standardIcon(icon);
    iconLabel->setPixmap(iconPixmap.scaled(32, 32, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    iconLabel->setAlignment(Qt::AlignTop);
    topLayout->addWidget(iconLabel);
    
    // 创建可滚动的文本区域
    QScrollArea* scrollArea = new QScrollArea();
    scrollArea->setWidgetResizable(true);
    scrollArea->setFrameShape(QFrame::NoFrame);
    
    QWidget* scrollContent = new QWidget();
    QVBoxLayout* scrollLayout = new QVBoxLayout(scrollContent);
    
    QLabel* textLabel = new QLabel(text);
    textLabel->setWordWrap(true);
    textLabel->setTextInteractionFlags(Qt::TextSelectableByMouse);
    scrollLayout->addWidget(textLabel);
    scrollLayout->addStretch();
    
    scrollArea->setWidget(scrollContent);
    topLayout->addWidget(scrollArea);
    
    layout->addLayout(topLayout);
    
    // 添加确定按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    
    QPushButton* okButton = new QPushButton(QObject::tr("确定"));
    QObject::connect(okButton, &QPushButton::clicked, &dialog, &QDialog::accept);
    buttonLayout->addWidget(okButton);
    
    layout->addLayout(buttonLayout);
    
    // 设置样式
    dialog.setStyleSheet(R"(
        QScrollArea {
            border: none;
            background-color: transparent;
        }
        
        QLabel {
            color: #2c3e50;
        }
    )");
    
    dialog.exec();
}

} // namespace utils
