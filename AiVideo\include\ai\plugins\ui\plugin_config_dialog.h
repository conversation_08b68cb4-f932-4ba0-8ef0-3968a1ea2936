#pragma once

#include <QCheckBox>
#include <QComboBox>
#include <QDialog>
#include <QDoubleSpinBox>
#include <QFormLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QScrollArea>
#include <QSpinBox>
#include <QVBoxLayout>

#include <map>
#include <sstream>

#include <memory>

#include "ai/plugins/task_plugin.h"
#include "core/project.h"

namespace ai {
namespace plugins {

/**
 * @brief 参数信息结构体
 */
struct ParamInfo {
    std::string type;           ///< 参数类型
    std::string description;    ///< 参数描述
    std::string default_value;  ///< 默认值
};

namespace ui {

/**
 * @brief 插件配置对话框基类
 */
class PluginConfigDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param plugin 插件指针
     * @param project 项目指针
     * @param parent 父窗口
     */
    explicit PluginConfigDialog(std::shared_ptr<ai::plugins::TaskPlugin> plugin,
                               std::shared_ptr<core::Project> project,
                               QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~PluginConfigDialog() override;

protected:
    /**
     * @brief 初始化UI
     */
    virtual void initUI();

    /**
     * @brief 保存配置
     */
    virtual void saveConfig();

    /**
     * @brief 加载配置
     */
    virtual void loadConfig();

    /**
     * @brief 获取插件参数信息
     * @return 参数信息映射表
     */
    std::map<std::string, ai::plugins::ParamInfo> getParamInfo();

    /**
     * @brief 重置插件状态
     */
    void resetPlugin();

    std::shared_ptr<ai::plugins::TaskPlugin> plugin_;  ///< 插件指针
    std::shared_ptr<core::Project> project_;           ///< 项目指针
    QVBoxLayout* mainLayout_;                          ///< 主布局
    QWidget* configWidget_;                            ///< 配置控件
    QCheckBox* enabledCheckBox_;                       ///< 启用复选框
    QPushButton* saveButton_;                          ///< 保存按钮
    QPushButton* cancelButton_;                        ///< 取消按钮
    QPushButton* resetButton_;                         ///< 重置按钮
    std::map<std::string, QWidget*> configWidgets_;    ///< 参数配置控件映射表
};

} // namespace ui
} // namespace plugins
} // namespace ai
