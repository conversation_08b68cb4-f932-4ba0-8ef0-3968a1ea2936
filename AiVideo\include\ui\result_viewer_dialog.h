#pragma once

#include <QCheckBox>
#include <QComboBox>
#include <QDialog>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QTimer>
#include <QVBoxLayout>

#include <memory>

#include "ai/frame_result.h"
#include "core/video_result_storage_server.h"

namespace ui {

/**
 * @brief 结果查看器对话框，用于实时显示处理结果
 */
class ResultViewerDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit ResultViewerDialog(QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~ResultViewerDialog();

    /**
     * @brief 设置结果存储服务器
     * @param server 结果存储服务器指针
     */
    void set_result_storage_server(std::shared_ptr<core::VideoResultStorageServer> server);

    /**
     * @brief 更新显示的结果
     * @param result 帧处理结果
     */
    void update_result(const ai::FrameResult& result);

    /**
     * @brief 更新服务状态
     * @param running 是否运行
     * @param port 端口
     * @param clientCount 客户端数量
     * @param protocol_type 协议类型，默认为TCP
     */
    void update_tcp_status(bool running, int port, int clientCount,
                          core::protocols::ProtocolType protocol_type = core::protocols::ProtocolType::TCP);

    /**
     * @brief 从存储服务器加载结果
     */
    void load_results_from_server();

    /**
     * @brief 启用自动刷新功能
     * @param enable 是否启用
     */
    void enable_auto_refresh(bool enable = true);

private slots:
    /**
     * @brief 自动滚动到底部
     */
    void auto_scroll_to_bottom();

    /**
     * @brief 清除显示内容
     */
    void clear_content();

    /**
     * @brief 保存显示内容到文件
     */
    void save_to_file();

    /**
     * @brief 显示模式改变
     * @param index 当前索引
     */
    void on_display_mode_changed(int index);

private:
    /**
     * @brief 初始化UI
     */
    void init_ui();

    /**
     * @brief 格式化JSON显示
     * @param json_str JSON字符串
     * @return 格式化后的字符串
     */
    QString format_json(const std::string& json_str);

    /**
     * @brief 提取关键信息显示
     * @param result 帧处理结果
     * @return 提取的关键信息
     */
    QString extract_key_info(const ai::FrameResult& result);

    QVBoxLayout* main_layout_;             ///< 主布局
    QTextEdit* result_text_edit_;          ///< 结果文本编辑器
    QPushButton* clear_button_;            ///< 清除按钮
    QPushButton* save_button_;             ///< 保存按钮
    QPushButton* load_button_;             ///< 加载按钮
    QCheckBox* auto_scroll_checkbox_;      ///< 自动滚动复选框
    QCheckBox* auto_refresh_checkbox_;     ///< 自动刷新复选框
    QComboBox* display_mode_combo_;        ///< 显示模式下拉框
    QLabel* status_label_;                 ///< 状态标签
    QLabel* tcp_status_label_;             ///< TCP状态标签
    QTimer* auto_scroll_timer_;            ///< 自动滚动定时器

    enum DisplayMode {
        FULL_JSON,                         ///< 完整JSON
        KEY_INFO                           ///< 关键信息
    };

    DisplayMode current_display_mode_;     ///< 当前显示模式
    int max_displayed_results_;            ///< 最大显示结果数
    int result_count_;                     ///< 结果计数

    std::shared_ptr<core::VideoResultStorageServer> result_storage_server_; ///< 结果存储服务器
    QTimer* refresh_timer_;                ///< 刷新定时器
};

} // namespace ui
