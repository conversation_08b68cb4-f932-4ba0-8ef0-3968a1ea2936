#pragma once

#include <Python.h>
#include <QCheckBox>
#include <QDialog>
#include <QHBoxLayout>
#include <QLabel>
#include <QListWidget>
#include <QMenu>
#include <QPushButton>
#include <QVBoxLayout>

#include <memory>

#include "ai/ai_processor.h"
#include "ai/plugins/python_script_manager.h"

namespace ui {

/**
 * @brief Python 脚本管理对话框
 */
class PythonScriptDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param processor AI处理器
     * @param parent 父窗口
     */
    explicit PythonScriptDialog(std::shared_ptr<ai::AiProcessor> processor, QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~PythonScriptDialog() override;

    /**
     * @brief 刷新脚本列表
     */
    void refreshScriptList();

private slots:
    /**
     * @brief 创建新脚本
     */
    void createNewScript();

    /**
     * @brief 加载脚本
     */
    void loadScript();

    /**
     * @brief 配置选中的脚本
     */
    void configureSelectedScript();

    /**
     * @brief 启用/禁用选中的脚本
     */
    void toggleSelectedScript();

    /**
     * @brief 处理脚本列表项双击
     * @param item 列表项
     */
    void onScriptItemDoubleClicked(QListWidgetItem* item);

    /**
     * @brief 处理脚本列表右键菜单
     * @param pos 位置
     */
    void onScriptListContextMenu(const QPoint& pos);

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 创建脚本列表项
     * @param script 脚本插件
     * @return 列表项
     */
    QListWidgetItem* createScriptItem(std::shared_ptr<ai::plugins::PythonScriptPlugin> script);

    /**
     * @brief 更新脚本列表项
     * @param item 列表项
     * @param script 脚本插件
     */
    void updateScriptItem(QListWidgetItem* item, std::shared_ptr<ai::plugins::PythonScriptPlugin> script);

    std::shared_ptr<ai::AiProcessor> processor_;  ///< AI处理器
    ai::plugins::PythonScriptManager script_manager_; ///< Python脚本管理器
    QVBoxLayout* mainLayout_;                     ///< 主布局
    QListWidget* scriptListWidget_;               ///< 脚本列表控件
    QPushButton* newButton_;                      ///< 新建按钮
    QPushButton* loadButton_;                     ///< 加载按钮
    QPushButton* configButton_;                   ///< 配置按钮
    QPushButton* toggleButton_;                   ///< 启用/禁用按钮
    QPushButton* closeButton_;                    ///< 关闭按钮
    QMenu* contextMenu_;                          ///< 右键菜单
};

} // namespace ui
