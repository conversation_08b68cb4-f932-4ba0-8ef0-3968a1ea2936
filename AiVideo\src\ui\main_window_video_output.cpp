#include "ui/main_window.h"
#include "utils/dialog_utils.h"
#include "utils/settings_manager.h"

#include <QDialog>
#include <QDialogButtonBox>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QVBoxLayout>
#include <QStatusBar>

namespace ui {

void MainWindow::modifyVideoOutputPath() {
    // 创建对话框
    QDialog dialog(this);
    dialog.setWindowTitle(tr("修改存储视频地址"));
    dialog.setMinimumWidth(500);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // 添加输出视频文件选择
    QHBoxLayout* outputFileLayout = new QHBoxLayout();
    QLabel* outputFileLabel = new QLabel(tr("输出视频文件:"), &dialog);
    QLineEdit* outputFileEdit = new QLineEdit(&dialog);
    outputFileEdit->setReadOnly(true);
    if (!videoOutputPath.isEmpty()) {
        outputFileEdit->setText(videoOutputPath);
    }
    QPushButton* browseFileButton = new QPushButton(tr("浏览..."), &dialog);
    outputFileLayout->addWidget(outputFileLabel);
    outputFileLayout->addWidget(outputFileEdit);
    outputFileLayout->addWidget(browseFileButton);
    layout->addLayout(outputFileLayout);

    // 添加说明文本
    QLabel* noteLabel = new QLabel(tr("注意: 修改存储视频地址后，新的视频将保存到指定位置。"), &dialog);
    noteLabel->setStyleSheet("color: #666; font-style: italic;");
    layout->addWidget(noteLabel);

    // 添加按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel, &dialog);
    layout->addWidget(buttonBox);

    // 连接信号
    connect(browseFileButton, &QPushButton::clicked, [&]() {
        QString file = QFileDialog::getSaveFileName(&dialog, tr("选择输出视频文件"),
                                                  QDir::homePath(),
                                                  tr("视频文件 (*.mp4 *.avi)"));
        if (!file.isEmpty()) {
            outputFileEdit->setText(file);
        }
    });

    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        QString newVideoOutputPath = outputFileEdit->text();
        
        if (newVideoOutputPath.isEmpty()) {
            utils::showScrollableMessageBox(this, tr("错误"),
                tr("请选择输出视频文件！"),
                QMessageBox::Warning);
            return;
        }

        // 如果当前正在录制视频，需要停止录制并重新开始
        bool wasRecording = isVideoRecording;
        if (wasRecording) {
            stopVideoRecording();
        }

        // 更新视频输出路径
        videoOutputPath = newVideoOutputPath;

        // 保存设置
        auto& settings = utils::SettingsManager::get_instance();
        settings.setValue("app/videoOutputPath", videoOutputPath.toStdString());

        // 显示成功消息
        this->statusBar()->showMessage(tr("视频存储地址已修改为: %1").arg(videoOutputPath), 5000);

        // 如果之前正在录制，重新开始录制
        if (wasRecording) {
            startVideoRecording();
        }
    }
}

} // namespace ui

