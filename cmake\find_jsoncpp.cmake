﻿include(FetchContent)
set(AIDIJSONC<PERSON>_DEPS_ROOT "http://pan.aqrose.com/f/d9519f2c317b43f7a5d0/?dl=1&p=jsoncpp.1.4.2.zip")
FetchContent_Declare(
    jsoncpp
    URL ${AIDIJSONC<PERSON>_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(jsoncpp)

if(NOT jsoncpp_POPULATED)
    FetchContent_Populate(jsoncpp)
    add_library(AIDIJSONCPP SHARED IMPORTED)
    set_target_properties(
        AIDIJSONCPP
        PROPERTIES
        IMPORTED_LOCATION
        ${jsoncpp_SOURCE_DIR}/build/Release/x64/jsoncpp.dll
        IMPORTED_LOCATION_DEBUG
        ${jsoncpp_SOURCE_DIR}/build/Release/x64/jsoncpp.dll
        IMPORTED_IMPLIB
        ${jsoncpp_SOURCE_DIR}/build/Release/x64/jsoncpp.lib
        IMPORTED_IMPLIB_DEBUG
        ${jsoncpp_SOURCE_DIR}/build/Debug/x64/jsoncpp.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${jsoncpp_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDIJSONCPP>
        DESTINATION release
    )
endif()
