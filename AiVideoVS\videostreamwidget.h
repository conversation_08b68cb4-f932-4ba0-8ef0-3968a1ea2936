#ifndef VIDEOSTREAMWIDGET_H
#define VIDEOSTREAMWIDGET_H

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTimer>
#include <QImage>
#include <QPixmap>
#include <QDialog>
#include <QKeyEvent>
#include <QCloseEvent>
#include <QResizeEvent>
#include <QShowEvent>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsPixmapItem>
#include <QGuiApplication>
#include <QScreen>
#include <opencv2/opencv.hpp>
#include <memory>
#include <json/json.h>

// 包含AiVideoCore的头文件
#include "ai/frame_result.h"
#include "utils/q_plugin_renderer.h"

class VideoStreamWidget : public QWidget
{
    Q_OBJECT

public:
    explicit VideoStreamWidget(QWidget *parent = nullptr);
    ~VideoStreamWidget();

    // 设置流ID
    void setStreamId(int id);

    // 设置流信息
    void setStreamInfo(const QString &videoPath, const QString &projectPath, int port = 8888);

    // 更新帧
    void updateFrame(const cv::Mat &frame);

    // 更新帧和处理结果
    void updateFrameWithResult(const cv::Mat &frame, const ai::FrameResult &result);

    // 获取流ID
    int getStreamId() const;

signals:
    void startStream(int streamId);
    void pauseStream(int streamId);
    void stopStream(int streamId);

private slots:
    void onStartButtonClicked();
    void onPauseButtonClicked();
    void onStopButtonClicked();
    void onFullscreenButtonClicked();

private:
    int streamId;
    QString videoPath;
    QString projectPath;
    int resultStoragePort;

    QGraphicsView *videoView;
    QLabel *infoLabel;
    QPushButton *startButton;
    QPushButton *pauseButton;
    QPushButton *stopButton;
    QPushButton *fullscreenButton;

    // 保存当前帧和处理结果
    cv::Mat currentFrame;
    ai::FrameResult currentResult;
    bool hasResult;

    // 渲染器
    std::unique_ptr<utils::QPluginRenderer> pluginRenderer;

    // 全屏对话框类的前置声明
    class FullscreenDialog;

    // 全屏对话框指针
    FullscreenDialog *fullscreenDialog;

    // 全屏对话框类
    class FullscreenDialog : public QDialog {
    public:
        explicit FullscreenDialog(QWidget *parent = nullptr);
        ~FullscreenDialog();
        void updateFromMat(const cv::Mat &frame);
        void updateFromMatWithResult(const cv::Mat &frame, const ai::FrameResult &result);

    protected:
        void keyPressEvent(QKeyEvent *event) override;
        void closeEvent(QCloseEvent *event) override;
        void resizeEvent(QResizeEvent *event) override;
        void showEvent(QShowEvent *event) override;

    private:
        QGraphicsView *videoView;
        cv::Mat currentFrame;
        ai::FrameResult currentResult;
        bool hasResult;
        std::unique_ptr<utils::QPluginRenderer> pluginRenderer;
    };
};

#endif // VIDEOSTREAMWIDGET_H
