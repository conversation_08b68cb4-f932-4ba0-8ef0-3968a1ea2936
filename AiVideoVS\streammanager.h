#ifndef STREAMMANAGER_H
#define STREAMMANAGER_H

#include <QObject>
#include <QList>
#include <QMap>
#include <QMutex>
#include <QThread>
#include <QWaitCondition>
#include <memory>
#include <opencv2/opencv.hpp>

#include "core/project_manager.h"
#include "core/video_processing_core.h"
#include "ai/plugins/python_script_manager.h"
#include "streamconfig.h"

// 视频流处理线程类
class StreamProcessingThread : public QThread
{
    Q_OBJECT

public:
    StreamProcessingThread(int streamId, QObject *parent = nullptr);
    ~StreamProcessingThread();

    // 设置流配置
    void setConfig(const StreamConfig &config);

    // 获取当前帧
    cv::Mat getCurrentFrame();

    // 获取当前处理结果
    ai::FrameResult getCurrentResult();

    // 控制函数
    void startProcessing();
    void pauseProcessing();
    void stopProcessing();

    // 获取状态
    bool isActive() const;
    bool isPaused() const;
    bool isVideoEnded() const;
    bool hasError() const;

    // 获取内部对象
    std::shared_ptr<core::Project> getProject() const;
    std::shared_ptr<core::VideoProcessingCore> getProcessor() const;

protected:
    void run() override;

private:
    int streamId;
    StreamConfig config;

    std::shared_ptr<core::Project> project;
    std::shared_ptr<core::VideoProcessingCore> processor;
    std::shared_ptr<ai::plugins::PythonScriptManager> scriptManager;

    cv::Mat currentFrame;
    ai::FrameResult currentResult;
    QMutex frameMutex;

    bool running;
    bool paused;
    bool videoEnded;
    bool error;

    QMutex controlMutex;
    QWaitCondition pauseCondition;

    // 初始化函数
    bool initialize();

    // 处理一帧
    bool processFrame();
};

// 流管理器类
class StreamManager : public QObject
{
    Q_OBJECT

public:
    explicit StreamManager(QObject *parent = nullptr);
    ~StreamManager();

    // 流配置管理
    void addStream();
    void addStream(const StreamConfig &config);
    void removeStream(int index);
    QList<StreamConfig> getStreamConfigs() const;

    // 更新流配置
    void updateVideoPath(int index, const QString &path);
    void updateProjectPath(int index, const QString &path);
    void updateResultStoragePort(int index, int port);

    // 流控制
    bool startStream(int index);
    bool pauseStream(int index);
    bool stopStream(int index);

    bool startAllStreams();
    bool pauseAllStreams();
    bool stopAllStreams();

    // 获取帧
    cv::Mat getFrame(int index);

    // 获取处理结果
    ai::FrameResult getResult(int index);

    // 配置保存和加载
    bool saveConfig(const QString &filePath);
    bool loadConfig(const QString &filePath);

private:
    QList<StreamConfig> streamConfigs;
    QMap<int, StreamProcessingThread*> processingThreads;

    QMutex configMutex;

    // 初始化函数
    void initialize();
};

#endif // STREAMMANAGER_H
