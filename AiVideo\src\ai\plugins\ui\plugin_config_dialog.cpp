#include "ai/plugins/ui/plugin_config_dialog.h"

#include <QMessageBox>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonValue>

namespace ai {
namespace plugins {
namespace ui {

PluginConfigDialog::PluginConfigDialog(std::shared_ptr<ai::plugins::TaskPlugin> plugin,
                                   std::shared_ptr<core::Project> project,
                                   QWidget* parent)
    : QDialog(parent), plugin_(plugin), project_(project) {

    setWindowTitle(QString::fromStdString(plugin_->get_display_name()));
    setMinimumSize(400, 300);

    initUI();
    loadConfig();
}

PluginConfigDialog::~PluginConfigDialog() {
}

void PluginConfigDialog::initUI() {
    mainLayout_ = new QVBoxLayout(this);

    // 创建启用复选框
    enabledCheckBox_ = new QCheckBox(tr("启用插件"), this);
    enabledCheckBox_->setChecked(plugin_->is_enabled());
    mainLayout_->addWidget(enabledCheckBox_);

    // 添加分隔线
    QFrame* line = new QFrame(this);
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    mainLayout_->addWidget(line);

    // 创建参数配置区域
    QScrollArea* scrollArea = new QScrollArea(this);
    scrollArea->setWidgetResizable(true);
    QWidget* scrollContent = new QWidget(scrollArea);
    QVBoxLayout* scrollLayout = new QVBoxLayout(scrollContent);

    // 创建参数控件
    configWidgets_.clear();

    // 获取插件的参数信息
    auto paramInfo = getParamInfo();

    // 获取插件参数值
    std::map<std::string, std::string> paramValues;

    // 从 Project 获取参数
    if (project_) {
        std::string pluginName = plugin_->get_name();
        paramValues = project_->get_plugin_params(pluginName);
    }

    // 如果 Project 中没有参数，从插件获取
    if (paramValues.empty()) {
        paramValues = plugin_->get_params();
    }

    // 如果仍然为空，获取默认参数
    if (paramValues.empty()) {
        paramValues = plugin_->get_default_params();
    }

    // 打印参数信息，用于调试
    std::cout << "Plugin: " << plugin_->get_name() << ", Parameters: " << paramValues.size() << std::endl;

    // 打印实际参数值
    for (const auto& param : paramValues) {
        std::cout << "  " << param.first << ": " << param.second << std::endl;
    }

    // 打印参数信息（包括类型和描述）
    if (!paramInfo.empty()) {
        std::cout << "Parameter Info: " << std::endl;
        for (const auto& param : paramInfo) {
            std::string value = "";
            if (paramValues.find(param.first) != paramValues.end()) {
                value = paramValues[param.first];
            } else if (!param.second.default_value.empty()) {
                value = param.second.default_value;
            }

            std::cout << "  " << param.first << ": " << param.second.description << " (type: " << param.second.type
                      << ", default: " << param.second.default_value << ", value: " << value << ")" << std::endl;
        }
    }

    // 创建参数表单
    QFormLayout* formLayout = new QFormLayout();

    // 如果有参数信息，使用参数信息创建控件
    if (!paramInfo.empty()) {
        // 使用参数信息创建控件
        // 为每个参数创建控件
        for (const auto& param : paramInfo) {
            const std::string& name = param.first;
            const auto& info = param.second;

            // 获取当前值（如果存在）
            std::string currentValue = "";
            if (paramValues.find(name) != paramValues.end()) {
                currentValue = paramValues[name];
            } else if (!info.default_value.empty()) {
                currentValue = info.default_value;
            }

            // 创建标签
            QString labelText = QString::fromStdString(name);
            if (!info.description.empty()) {
                labelText += " (" + QString::fromStdString(info.description) + ")";
            }

            // 创建输入控件
            QWidget* inputWidget = nullptr;

            // 根据参数类型创建不同的控件
            if (info.type == "boolean" ||
                name == "show_debug_info" ||
                name.find("enable") != std::string::npos ||
                name.find("enabled") != std::string::npos) {
                // 布尔类型参数
                QCheckBox* checkBox = new QCheckBox(scrollContent);
                bool isChecked = (currentValue == "true" || currentValue == "1" ||
                                 currentValue == "yes" || currentValue == "y" ||
                                 currentValue == "on");
                checkBox->setChecked(isChecked);
                inputWidget = checkBox;
            } else if (info.type == "integer" ||
                      name.find("count") != std::string::npos ||
                      name.find("threshold") != std::string::npos ||
                      name.find("interval") != std::string::npos) {
                // 整数类型参数
                QSpinBox* spinBox = new QSpinBox(scrollContent);
                spinBox->setRange(0, 1000);
                if (!currentValue.empty()) {
                    try {
                        spinBox->setValue(std::stoi(currentValue));
                    } catch (...) {
                        // 转换失败，使用默认值
                    }
                }
                inputWidget = spinBox;
            } else if (info.type == "float" ||
                      name.find("line_y") != std::string::npos ||
                      name.find("line_x") != std::string::npos) {
                // 浮点类型参数
                QDoubleSpinBox* doubleSpinBox = new QDoubleSpinBox(scrollContent);
                doubleSpinBox->setRange(0.0, 1.0);
                doubleSpinBox->setSingleStep(0.1);
                if (!currentValue.empty()) {
                    try {
                        doubleSpinBox->setValue(std::stod(currentValue));
                    } catch (...) {
                        // 转换失败，使用默认值
                    }
                }
                inputWidget = doubleSpinBox;
            } else if (info.type == "string" &&
                      (name.find("mode") != std::string::npos ||
                       name.find("type") != std::string::npos)) {
                // 枚举类型参数
                QComboBox* comboBox = new QComboBox(scrollContent);

                // 尝试从描述中提取可能的选项
                std::string desc = info.description;
                if (desc.find("可选值") != std::string::npos) {
                    size_t start = desc.find("可选值") + 4;
                    while (start < desc.length() && desc[start] != ':') start++;
                    if (start < desc.length()) {
                        start++; // 跳过冒号

                        // 提取选项
                        std::string options = desc.substr(start);
                        std::string option;
                        std::istringstream stream(options);

                        while (std::getline(stream, option, ',')) {
                            // 去除前后空格和引号
                            size_t first = option.find_first_not_of(" '\"");
                            size_t last = option.find_last_not_of(" '\"");
                            if (first != std::string::npos && last != std::string::npos) {
                                option = option.substr(first, last - first + 1);
                                comboBox->addItem(QString::fromStdString(option));
                            }
                        }
                    }
                }

                // 如果没有从描述中提取到选项，添加一些默认选项
                if (comboBox->count() == 0) {
                    if (name.find("mode") != std::string::npos) {
                        comboBox->addItem("simple");
                        comboBox->addItem("advanced");
                    }
                }

                // 设置当前值
                if (!currentValue.empty()) {
                    comboBox->setCurrentText(QString::fromStdString(currentValue));
                }

                inputWidget = comboBox;
            } else {
                // 默认使用文本输入框
                QLineEdit* lineEdit = new QLineEdit(scrollContent);
                lineEdit->setText(QString::fromStdString(currentValue));
                inputWidget = lineEdit;
            }

            // 存储控件
            configWidgets_[name] = inputWidget;

            // 添加到表单
            formLayout->addRow(labelText, inputWidget);
        }
    } else if (!paramValues.empty()) {
        // 如果没有参数信息但有参数值，根据参数值创建控件
        for (const auto& param : paramValues) {
            const std::string& name = param.first;
            const std::string& value = param.second;

            // 创建标签
            QString labelText = QString::fromStdString(name);

            // 创建输入控件
            QWidget* inputWidget = nullptr;

            // 根据参数名称和值猜测类型
            if (name.find("enabled") != std::string::npos ||
                name.find("enable") != std::string::npos ||
                name.find("debug") != std::string::npos ||
                value == "true" || value == "false" ||
                value == "1" || value == "0" ||
                value == "yes" || value == "no" ||
                value == "y" || value == "n" ||
                value == "on" || value == "off") {
                // 布尔类型参数
                QCheckBox* checkBox = new QCheckBox(scrollContent);
                bool isChecked = (value == "true" || value == "1" ||
                                 value == "yes" || value == "y" ||
                                 value == "on");
                checkBox->setChecked(isChecked);
                inputWidget = checkBox;
            } else if (name.find("count") != std::string::npos ||
                      name.find("interval") != std::string::npos ||
                      name.find("threshold") != std::string::npos) {
                // 整数类型参数
                QSpinBox* spinBox = new QSpinBox(scrollContent);
                spinBox->setRange(0, 1000);
                if (!value.empty()) {
                    try {
                        spinBox->setValue(std::stoi(value));
                    } catch (...) {
                        // 转换失败，使用默认值
                    }
                }
                inputWidget = spinBox;
            } else if (name.find("line_y") != std::string::npos ||
                      name.find("line_x") != std::string::npos) {
                // 浮点类型参数
                QDoubleSpinBox* doubleSpinBox = new QDoubleSpinBox(scrollContent);
                doubleSpinBox->setRange(0.0, 1.0);
                doubleSpinBox->setSingleStep(0.1);
                if (!value.empty()) {
                    try {
                        doubleSpinBox->setValue(std::stod(value));
                    } catch (...) {
                        // 转换失败，使用默认值
                    }
                }
                inputWidget = doubleSpinBox;
            } else if (name.find("mode") != std::string::npos ||
                      name.find("type") != std::string::npos) {
                // 枚举类型参数
                QComboBox* comboBox = new QComboBox(scrollContent);

                // 添加一些默认选项
                if (name.find("mode") != std::string::npos) {
                    comboBox->addItem("simple");
                    comboBox->addItem("advanced");
                }

                // 设置当前值
                if (!value.empty()) {
                    comboBox->setCurrentText(QString::fromStdString(value));
                }

                inputWidget = comboBox;
            } else {
                // 默认使用文本输入框
                QLineEdit* lineEdit = new QLineEdit(scrollContent);
                lineEdit->setText(QString::fromStdString(value));
                inputWidget = lineEdit;
            }

            // 存储控件
            configWidgets_[name] = inputWidget;

            // 添加到表单
            formLayout->addRow(labelText, inputWidget);
        }
    } else {
        // 如果没有参数信息也没有参数值，显示一个提示
        QLabel* noConfigLabel = new QLabel(tr("此插件没有提供配置选项。"), scrollContent);
        noConfigLabel->setAlignment(Qt::AlignCenter);
        scrollLayout->addWidget(noConfigLabel);
    }

    if (!paramInfo.empty() || !paramValues.empty()) {
        scrollLayout->addLayout(formLayout);
    }

    scrollLayout->addStretch();
    scrollContent->setLayout(scrollLayout);
    scrollArea->setWidget(scrollContent);
    mainLayout_->addWidget(scrollArea);

    // 添加弹簧
    mainLayout_->addStretch();

    // 创建按钮布局
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    // 创建重置按钮
    resetButton_ = new QPushButton(tr("重置"), this);
    connect(resetButton_, &QPushButton::clicked, this, &PluginConfigDialog::resetPlugin);
    buttonLayout->addWidget(resetButton_);

    // 添加弹簧，使重置按钮靠左，保存和取消按钮靠右
    buttonLayout->addStretch();

    // 创建保存按钮
    saveButton_ = new QPushButton(tr("保存"), this);
    connect(saveButton_, &QPushButton::clicked, this, &PluginConfigDialog::saveConfig);
    buttonLayout->addWidget(saveButton_);

    // 创建取消按钮
    cancelButton_ = new QPushButton(tr("取消"), this);
    connect(cancelButton_, &QPushButton::clicked, this, &PluginConfigDialog::reject);
    buttonLayout->addWidget(cancelButton_);

    mainLayout_->addLayout(buttonLayout);

    setLayout(mainLayout_);
}

void PluginConfigDialog::saveConfig() {
    // 设置插件是否启用
    plugin_->set_enabled(enabledCheckBox_->isChecked());

    // 保存参数
    std::map<std::string, std::string> params;

    // 从控件中获取参数值
    for (const auto& pair : configWidgets_) {
        const std::string& name = pair.first;
        QWidget* widget = pair.second;

        if (QCheckBox* checkBox = qobject_cast<QCheckBox*>(widget)) {
            params[name] = checkBox->isChecked() ? "true" : "false";
        } else if (QSpinBox* spinBox = qobject_cast<QSpinBox*>(widget)) {
            params[name] = std::to_string(spinBox->value());
        } else if (QDoubleSpinBox* doubleSpinBox = qobject_cast<QDoubleSpinBox*>(widget)) {
            params[name] = std::to_string(doubleSpinBox->value());
        } else if (QComboBox* comboBox = qobject_cast<QComboBox*>(widget)) {
            params[name] = comboBox->currentText().toStdString();
        } else if (QLineEdit* lineEdit = qobject_cast<QLineEdit*>(widget)) {
            params[name] = lineEdit->text().toStdString();
        }
    }

    // 设置插件参数
    plugin_->set_params(params);

    // 保存到 Project
    if (project_) {
        std::string pluginName = plugin_->get_name();

        // 打印调试信息
        std::cout << "保存插件 " << pluginName << " 的参数到 Project：" << std::endl;
        for (const auto& param : params) {
            std::cout << "  " << param.first << ": " << param.second << std::endl;
        }

        project_->set_plugin_params(pluginName, params);

        // 验证参数是否正确保存
        auto savedParams = project_->get_plugin_params(pluginName);
        std::cout << "从 Project 获取插件 " << pluginName << " 的参数：" << std::endl;
        for (const auto& param : savedParams) {
            std::cout << "  " << param.first << ": " << param.second << std::endl;
        }

        // 更新启用状态
        std::vector<std::string> enabledPlugins = project_->get_enabled_plugins();
        bool isEnabled = plugin_->is_enabled();
        bool isInEnabledList = std::find(enabledPlugins.begin(), enabledPlugins.end(), pluginName) != enabledPlugins.end();

        if (isEnabled && !isInEnabledList) {
            // 如果插件被启用但不在启用列表中，添加到列表
            enabledPlugins.push_back(pluginName);
            project_->set_enabled_plugins(enabledPlugins);
        } else if (!isEnabled && isInEnabledList) {
            // 如果插件被禁用但在启用列表中，从列表中移除
            auto it = std::find(enabledPlugins.begin(), enabledPlugins.end(), pluginName);
            if (it != enabledPlugins.end()) {
                enabledPlugins.erase(it);
                project_->set_enabled_plugins(enabledPlugins);
            }
        }
    }

    QMessageBox::information(this, tr("保存成功"), tr("插件配置已保存。"));
    accept();
}

std::map<std::string, ai::plugins::ParamInfo> PluginConfigDialog::getParamInfo() {
    std::map<std::string, ai::plugins::ParamInfo> paramInfo;

    // 获取插件参数
    std::map<std::string, std::string> params;

    // 从 Project 获取参数
    if (project_) {
        std::string pluginName = plugin_->get_name();
        params = project_->get_plugin_params(pluginName);
    }

    // 如果 Project 中没有参数，从插件获取
    if (params.empty()) {
        params = plugin_->get_params();
    }

    // 尝试从插件的 get_info 方法获取参数信息
    std::string pluginType = plugin_->get_type();

    // 尝试从插件描述中获取参数信息
    std::string description = plugin_->get_description();

    // 检查描述中是否包含参数信息
    size_t paramsPos = description.find("参数：");
    if (paramsPos != std::string::npos) {
        // 提取参数部分
        std::string paramsPart = description.substr(paramsPos + 3);
        std::istringstream stream(paramsPart);
        std::string line;

        // 逐行解析参数
        while (std::getline(stream, line)) {
            // 跳过空行
            if (line.empty()) {
                continue;
            }

            // 查找参数名和描述的分隔符
            size_t colonPos = line.find(":");
            if (colonPos == std::string::npos) {
                colonPos = line.find("：");
            }

            if (colonPos != std::string::npos) {
                // 提取参数名和描述
                std::string paramName = line.substr(0, colonPos);
                std::string paramDesc = line.substr(colonPos + 1);

                // 去除前后空格
                paramName.erase(0, paramName.find_first_not_of(" -"));
                paramName.erase(paramName.find_last_not_of(" ") + 1);
                paramDesc.erase(0, paramDesc.find_first_not_of(" "));
                paramDesc.erase(paramDesc.find_last_not_of(" ") + 1);

                ai::plugins::ParamInfo info;
                info.description = paramDesc;

                // 尝试猜测参数类型
                if (paramName.find("enabled") != std::string::npos ||
                    paramName.find("enable") != std::string::npos ||
                    paramName.find("debug") != std::string::npos) {
                    info.type = "boolean";
                } else if (paramName.find("count") != std::string::npos ||
                          paramName.find("interval") != std::string::npos ||
                          paramName.find("threshold") != std::string::npos) {
                    info.type = "integer";
                } else if (paramName.find("line_y") != std::string::npos ||
                          paramName.find("line_x") != std::string::npos) {
                    info.type = "float";
                } else if (paramName.find("mode") != std::string::npos ||
                          paramName.find("type") != std::string::npos) {
                    info.type = "string";
                } else {
                    info.type = "string";
                }

                // 获取当前值作为默认值
                if (params.find(paramName) != params.end()) {
                    info.default_value = params[paramName];
                }

                paramInfo[paramName] = info;
            }
        }
    }

    // 如果没有获取到参数信息，尝试从参数名称猜测
    if (paramInfo.empty()) {
        for (const auto& param : params) {
            const std::string& name = param.first;
            const std::string& value = param.second;

            ai::plugins::ParamInfo info;
            info.default_value = value;

            // 尝试猜测参数类型
            if (name.find("enabled") != std::string::npos ||
                name.find("enable") != std::string::npos ||
                name.find("debug") != std::string::npos) {
                info.type = "boolean";
            } else if (name.find("count") != std::string::npos ||
                      name.find("interval") != std::string::npos ||
                      name.find("threshold") != std::string::npos) {
                info.type = "integer";
            } else if (name.find("line_y") != std::string::npos ||
                      name.find("line_x") != std::string::npos) {
                info.type = "float";
            } else if (name.find("mode") != std::string::npos ||
                      name.find("type") != std::string::npos) {
                info.type = "string";
            } else {
                info.type = "string";
            }

            paramInfo[name] = info;
        }
    }

    return paramInfo;
}

void PluginConfigDialog::loadConfig() {
    // 设置启用复选框状态
    enabledCheckBox_->setChecked(plugin_->is_enabled());

    // 获取插件当前参数
    auto currentParams = plugin_->get_params();

    // 从 Project 获取插件参数
    std::map<std::string, std::string> projectParams;
    if (project_) {
        std::string pluginName = plugin_->get_name();
        projectParams = project_->get_plugin_params(pluginName);
    }

    // 如果 Project 中有参数，设置到插件
    if (!projectParams.empty()) {
        plugin_->set_params(projectParams);
    } else {
        // 如果 Project 中没有参数，但插件当前也没有参数，尝试获取默认参数
        if (currentParams.empty()) {
            // 获取插件参数信息，其中包含默认值
            auto paramInfo = getParamInfo();

            // 从参数信息中提取默认值
            std::map<std::string, std::string> defaultParams;
            for (const auto& param : paramInfo) {
                const std::string& name = param.first;
                const auto& info = param.second;

                // 如果有默认值，添加到默认参数中
                if (!info.default_value.empty()) {
                    defaultParams[name] = info.default_value;
                }
            }

            // 如果有默认参数，设置到插件
            if (!defaultParams.empty()) {
                plugin_->set_params(defaultParams);

                // 同时保存到 Project
                if (project_) {
                    std::string pluginName = plugin_->get_name();
                    project_->set_plugin_params(pluginName, defaultParams);
                }

                std::cout << "设置插件 " << plugin_->get_name() << " 的默认参数：" << std::endl;
                for (const auto& param : defaultParams) {
                    std::cout << "  " << param.first << ": " << param.second << std::endl;
                }
            }
        }
    }
}

void PluginConfigDialog::resetPlugin() {
    // 确认对话框
    QMessageBox::StandardButton reply = QMessageBox::question(
        this,
        tr("重置插件"),
        tr("确定要重置插件状态吗？这将清除插件的当前状态。"),
        QMessageBox::Yes | QMessageBox::No
    );

    if (reply == QMessageBox::Yes) {
        // 调用插件的reset函数
        plugin_->reset();

        // 显示成功消息
        QMessageBox::information(this, tr("重置成功"), tr("插件状态已重置。"));

        // 可选：重新加载配置
        loadConfig();
    }
}

} // namespace ui
} // namespace plugins
} // namespace ai
