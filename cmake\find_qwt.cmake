﻿include(FetchContent)
set(QWT_DEPS_ROOT "http://pan.aqrose.com/f/85b648f74b2947db8984/?dl=1&p=QWT.zip")
FetchContent_Declare(
    qwt
    URL ${QWT_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(qwt)

if(NOT qwt_POPULATED)
    FetchContent_Populate(qwt)
    add_library(AIDIQWT SHARED IMPORTED)
    set_target_properties(
        AIDIQWT
        PROPERTIES
        IMPORTED_LOCATION
        ${qwt_SOURCE_DIR}/build/Release/x64/qwt.dll
        IMPORTED_LOCATION_DEBUG
        ${qwt_SOURCE_DIR}/build/Release/x64/qwt.dll
        IMPORTED_IMPLIB
        ${qwt_SOURCE_DIR}/build/Release/x64/qwt.lib
        IMPORTED_IMPLIB_DEBUG
        ${qwt_SOURCE_DIR}/build/Release/x64/qwt.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${qwt_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDIQWT>
        DESTINATION release
    )
endif()
