#ifndef STREAMCONFIG_H
#define STREAMCONFIG_H

#include <QString>

// 视频流配置结构体
struct StreamConfig {
    int id;                     // 流ID
    QString videoPath;          // 视频路径
    QString projectPath;        // 项目路径
    bool isActive;              // 是否激活
    bool isPaused;              // 是否暂停
    int resultStoragePort;      // 结果存储服务器端口
    bool isVideoEnded;          // 视频是否已结束
    bool hasError;              // 是否发生错误
    
    // 构造函数
    StreamConfig() : id(0), isActive(false), isPaused(false), 
                    resultStoragePort(8888), isVideoEnded(false), hasError(false) {}
};

#endif // STREAMCONFIG_H
