﻿include(FetchContent)
set(CUDA_DEPS_ROOT "http://pan.aqrose.com/f/1b2b92cba59040809a61/?dl=1&p=cudav11.1.zip")
FetchContent_Declare(
    cuda
    URL ${CUDA_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(cuda)

if(NOT cuda_POPULATED)
    FetchContent_Populate(cuda)
    add_library(AIDICUDA SHARED IMPORTED)
    set_target_properties(
        AIDICUDA
        PROPERTIES
        IMPORTED_LOCATION
        ${cuda_SOURCE_DIR}/build/Release/x64/cudart64_110.dll
        IMPORTED_LOCATION_DEBUG
        ${cuda_SOURCE_DIR}/build/Release/x64/cudart64_110.dll
        IMPORTED_IMPLIB
        ${cuda_SOURCE_DIR}/build/Release/x64/cudart.lib
        IMPORTED_IMPLIB_DEBUG
        ${cuda_SOURCE_DIR}/build/Debug/x64/cudart.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${cuda_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDICUDA>
        DESTINATION release
    )
endif()
