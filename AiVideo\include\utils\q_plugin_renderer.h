#pragma once

#include <QGraphicsScene>
#include <QGraphicsView>
#include <QGraphicsItem>
#include <QGraphicsTextItem>
#include <QGraphicsRectItem>
#include <QGraphicsLineItem>
#include <QGraphicsEllipseItem>
#include <QGraphicsPixmapItem>
#include <QPixmap>
#include <QColor>
#include <QFont>
#include <QMap>
#include <QString>
#include <QVariant>

#include <opencv2/opencv.hpp>
#include <json/json.h>

#include "ai/frame_result.h"

namespace utils {

/**
 * @brief 基于QT图元的插件渲染器
 *
 * 用于渲染插件处理结果，替代AiVideoCore库提供的utils::PluginRenderer
 */
class QPluginRenderer {
public:
    /**
     * @brief 构造函数
     */
    QPluginRenderer();

    /**
     * @brief 析构函数
     */
    ~QPluginRenderer();

    /**
     * @brief 渲染插件处理结果到图像上（兼容旧接口）
     *
     * @param frame 要渲染的图像
     * @param render_info 渲染信息JSON对象
     * @return 是否成功渲染
     */
    bool render(cv::Mat& frame, const Json::Value& render_info);

    /**
     * @brief 渲染插件处理结果到图像上（兼容旧接口）
     *
     * @param frame 要渲染的图像
     * @param result 处理结果
     * @return 是否成功渲染
     */
    bool render_frame_result(cv::Mat& frame, const ai::FrameResult& result);

    /**
     * @brief 直接渲染到QGraphicsView
     *
     * @param view 目标QGraphicsView
     * @param frame 原始图像
     * @param render_info 渲染信息JSON对象
     * @return 是否成功渲染
     */
    bool render_to_view(QGraphicsView* view, const cv::Mat& frame, const Json::Value& render_info);

    /**
     * @brief 直接渲染到QGraphicsView
     *
     * @param view 目标QGraphicsView
     * @param frame 原始图像
     * @param result 处理结果
     * @return 是否成功渲染
     */
    bool render_frame_result_to_view(QGraphicsView* view, const cv::Mat& frame, const ai::FrameResult& result);

    /**
     * @brief 直接渲染图形元素到QGraphicsView，不处理图像数据
     *
     * @param view 目标QGraphicsView
     * @param frame_size 图像尺寸
     * @param render_info 渲染信息JSON对象
     * @return 是否成功渲染
     */
    bool render_graphics_to_view(QGraphicsView* view, const QSize& frame_size, const Json::Value& render_info);

    /**
     * @brief 直接渲染图形元素到QGraphicsView，不处理图像数据
     *
     * @param view 目标QGraphicsView
     * @param frame_size 图像尺寸
     * @param result 处理结果
     * @return 是否成功渲染
     */
    bool render_graphics_frame_result_to_view(QGraphicsView* view, const QSize& frame_size, const ai::FrameResult& result);

    /**
     * @brief 创建QGraphicsScene并渲染结果
     *
     * @param frame 要渲染的图像
     * @param render_info 渲染信息JSON对象
     * @return 渲染后的QGraphicsScene
     */
    QGraphicsScene* create_scene(const cv::Mat& frame, const Json::Value& render_info);

    /**
     * @brief 将QGraphicsScene渲染到OpenCV图像
     *
     * @param scene 要渲染的场景
     * @param frame 目标图像
     */
    void render_scene_to_frame(QGraphicsScene* scene, cv::Mat& frame);

    /**
     * @brief 清除QGraphicsView中的所有图元
     *
     * @param view 要清除的QGraphicsView
     */
    void clear_view(QGraphicsView* view);

    /**
     * @brief 将OpenCV图像转换为QImage
     *
     * @param frame OpenCV图像
     * @return QImage对象
     */
    static QImage mat_to_qimage(const cv::Mat& mat) {
            // 处理单通道图像（灰度图）
        if (mat.type() == CV_8UC1) {
            // 创建一个 QImage 对象，直接使用 cv::Mat 的数据
            QImage image(mat.data, mat.cols, mat.rows, mat.step, QImage::Format_Grayscale8);
            // 返回 QImage 的深拷贝，避免数据生命周期问题
            return image.copy();
        } 
        // 处理三通道图像（BGR 格式）
        else if (mat.type() == CV_8UC3) {
            // 创建一个 QImage 对象，使用 cv::Mat 的数据
            // 注意：OpenCV 使用 BGR 顺序，而 QImage 的 Format_RGB888 需要 RGB 顺序
            // 这里应该使用 Format_BGR888 格式，然后再交换 R 和 B 通道
            cv::Mat rgbMat;
            cv::cvtColor(mat, rgbMat, cv::COLOR_BGR2RGB);
            QImage image(rgbMat.data, rgbMat.cols, rgbMat.rows, rgbMat.step, QImage::Format_BGR888);
            // 交换 R 和 B 通道，将 BGR 转换为 RGB
            return image.rgbSwapped();
        } 
        // 处理四通道图像（BGRA 格式）
        else if (mat.type() == CV_8UC4) {
            // 创建一个 QImage 对象，使用 cv::Mat 的数据，并转换为 ARGB 格式
            QImage image(mat.data, mat.cols, mat.rows, mat.step, QImage::Format_ARGB32);
            // 返回 QImage 的深拷贝，避免数据生命周期问题
            return image.copy();
        } 
        // 其他类型的图像，返回一个空的 QImage
        else {
            return QImage();
        }
    }
    

private:
    int transparency_ = 128;
    /**
     * @brief 渲染跟踪目标
     *
     * @param scene 场景
     * @param tracks 跟踪目标信息
     * @param frame_size 图像尺寸
     */
    void render_tracks(QGraphicsScene* scene, const Json::Value& tracks, const QSize& frame_size);

    /**
     * @brief 渲染自定义元素
     *
     * @param scene 场景
     * @param custom_elements 自定义元素信息
     * @param frame_size 图像尺寸
     */
    void render_custom_elements(QGraphicsScene* scene, const Json::Value& custom_elements, const QSize& frame_size);

    /**
     * @brief 渲染状态信息
     *
     * @param scene 场景
     * @param status 状态信息
     * @param frame_size 图像尺寸
     */
    void render_status(QGraphicsScene* scene, const Json::Value& status, const QSize& frame_size);

    /**
     * @brief 渲染帧信息
     *
     * @param scene 场景
     * @param frame_info 帧信息
     * @param frame_size 图像尺寸
     */
    void render_frame_info(QGraphicsScene* scene, const Json::Value& frame_info, const QSize& frame_size);

    /**
     * @brief 渲染已完成步骤
     *
     * @param scene 场景
     * @param completed_steps 已完成步骤信息
     * @param frame_size 图像尺寸
     */
    void render_completed_steps(QGraphicsScene* scene, const Json::Value& completed_steps, const QSize& frame_size);

    /**
     * @brief 将BGR颜色转换为QColor
     *
     * @param bgr BGR颜色数组
     * @return QColor对象
     */
    QColor bgr_to_qcolor(const Json::Value& bgr);

    /**
     * @brief 获取默认字体
     *
     * @param size 字体大小
     * @return QFont对象
     */
    QFont get_default_font(int size = 12);

    /**
     * @brief 创建文本项
     *
     * @param text 文本内容
     * @param x X坐标
     * @param y Y坐标
     * @param color 文本颜色
     * @param font_size 字体大小
     * @return 文本图元
     */
    QGraphicsTextItem* create_text_item(const QString& text, qreal x, qreal y,
                                       const QColor& color, int font_size = 12);


};

} // namespace utils
