#pragma once

#include <QComboBox>
#include <QDialog>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QLabel>
#include <QMessageBox>
#include <QPushButton>
#include <QTextEdit>
#include <QVBoxLayout>

#include <memory>

#include "ai/python_frame_processor.h"

namespace ui {

/**
 * @brief 帧处理脚本编辑器对话框
 */
class FrameProcessorEditorDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param processor Python帧处理器
     * @param parent 父窗口
     */
    explicit FrameProcessorEditorDialog(std::shared_ptr<ai::PythonFrameProcessor> processor, QWidget* parent = nullptr);

private slots:
    /**
     * @brief 加载脚本
     */
    void loadScript();

    /**
     * @brief 保存脚本
     */
    void saveScript();

    /**
     * @brief 重新加载脚本
     */
    void reloadScript();

    /**
     * @brief 创建新脚本
     */
    void createNewScript();

    /**
     * @brief 脚本选择改变
     * @param index 索引
     */
    void scriptSelectionChanged(int index);

private:
    /**
     * @brief 更新脚本列表
     */
    void updateScriptList();

    /**
     * @brief 加载脚本内容
     * @param filePath 脚本路径
     */
    void loadScriptContent(const QString& filePath);

    std::shared_ptr<ai::PythonFrameProcessor> processor_;  ///< Python帧处理器
    QComboBox* scriptComboBox_;                           ///< 脚本选择下拉框
    QTextEdit* scriptEditor_;                             ///< 脚本编辑器
    QPushButton* loadButton_;                             ///< 加载按钮
    QPushButton* saveButton_;                             ///< 保存按钮
    QPushButton* reloadButton_;                           ///< 重新加载按钮
    QPushButton* newButton_;                              ///< 新建按钮
    QLabel* statusLabel_;                                 ///< 状态标签
    QString currentScriptPath_;                           ///< 当前脚本路径
    QString scriptDirectory_;                             ///< 脚本目录
};

} // namespace ui

