﻿include(FetchContent)
set(AQCBB_DEPS_ROOT "https://pan.aqrose.com/f/8b603bc98cc64d6fb223/?dl=1&p=code-editor-Daily-20240927.zip")
FetchContent_Declare(
    aqcbb
    URL ${AQCBB_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(aqcbb)

if(NOT aqcbb_POPULATED)
    FetchContent_Populate(aqcbb)
    add_library(AIDIAQCbb SHARED IMPORTED)
    set_target_properties(
        AIDIAQCbb
        PROPERTIES
        IMPORTED_LOCATION
        ${aqcbb_SOURCE_DIR}/bin/x64-Release/code-editor.dll
        IMPORTED_LOCATION_DEBUG
        ${aqcbb_SOURCE_DIR}/bin/x64-Release/code-editor.dll
        IMPORTED_IMPLIB
        ${aqcbb_SOURCE_DIR}/lib/x64-Release/code-editor.lib
        IMPORTED_IMPLIB_DEBUG
        ${aqcbb_SOURCE_DIR}/lib/x64-Release/code-editor.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${aqcbb_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDIAQCbb>
        DESTINATION release
    )
endif()
