﻿
include(FetchContent)

set(lib_resource_path "http://pan.aqrose.com/f/829f29f2925c446d9880/?dl=1&p=Aquth-0.0.9-alpha.unknown-msvcrt-x64_windows-opt.tar.gz")

FetchContent_Declare(
  aquth
  URL ${lib_resource_path}
  UPDATE_DISCONNECTED
)

FetchContent_GetProperties(aquth)

if(NOT aquth_POPULATED)
    FetchContent_Populate(aquth)
    
    add_library(Aquth STATIC IMPORTED)
    set_target_properties(
        Aquth
        PROPERTIES
        IMPORTED_LOCATION 
        ${aquth_SOURCE_DIR}/lib/aquth.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${aquth_SOURCE_DIR}/include
    )

    add_library(AquthRuntime STATIC IMPORTED)
    set_target_properties(
        AquthRuntime
        PROPERTIES
        IMPORTED_LOCATION 
        ${aquth_SOURCE_DIR}/lib/slm_runtime_api_dev.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${aquth_SOURCE_DIR}/include
    )

    add_library(AquthControl STATIC IMPORTED)
    set_target_properties(
        AquthControl
        PROPERTIES
        IMPORTED_LOCATION 
        ${aquth_SOURCE_DIR}/lib/slm_control_api.lib
        INTERFACE_INCLUDE_DIRECTORIES
        ${aquth_SOURCE_DIR}/include
    )
endif()

