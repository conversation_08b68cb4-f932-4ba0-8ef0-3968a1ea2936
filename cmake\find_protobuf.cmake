﻿include(FetchContent)
set(PROTOBUF_DEPS_ROOT "http://pan.aqrose.com/f/85b2f2c0b3124cceb632/?dl=1&p=protobuf.3.5.1.zip")
FetchContent_Declare(
    protobuf
    URL ${PROTOBUF_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(protobuf)

if(NOT protobuf_POPULATED)
    FetchContent_Populate(protobuf)
    add_library(AIDIPROTOBUF::libprotobuf SHARED IMPORTED)
    set_target_properties(
        AIDIPROTOBUF::libprotobuf
        PROPERTIES
        IMPORTED_IMPLIB
        ${protobuf_SOURCE_DIR}/build/Release/x64/libprotobuf.lib
        IMPORTED_IMPLIB_DEBUG
        ${protobuf_SOURCE_DIR}/build/Debug/x64/libprotobufd.lib
        IMPORTED_IMPLIB_RELEASE
        ${protobuf_SOURCE_DIR}/build/Release/x64/libprotobuf.lib
        IMPORTED_LOCATION
        ${protobuf_SOURCE_DIR}/bin/libprotobuf.dll
        IMPORTED_LOCATION_DEBUG
        ${protobuf_SOURCE_DIR}/bin/libprotobufd.dll
        IMPORTED_LOCATION_RELEASE
        ${protobuf_SOURCE_DIR}/bin/libprotobuf.dll
        INTERFACE_INCLUDE_DIRECTORIES
        ${protobuf_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDIPROTOBUF::libprotobuf>
        DESTINATION release
    )

    add_library(AIDIPROTOBUF::libprotoc SHARED IMPORTED)
    set_target_properties(
        AIDIPROTOBUF::libprotoc
        PROPERTIES
        IMPORTED_IMPLIB
        ${protobuf_SOURCE_DIR}/build/Release/x64/libprotoc.lib
        IMPORTED_IMPLIB_DEBUG
        ${protobuf_SOURCE_DIR}/build/Debug/x64/libprotocd.lib
        IMPORTED_IMPLIB_RELEASE
        ${protobuf_SOURCE_DIR}/build/Release/x64/libprotoc.lib
        IMPORTED_LOCATION
        ${protobuf_SOURCE_DIR}/bin/libprotoc.dll
        IMPORTED_LOCATION_DEBUG
        ${protobuf_SOURCE_DIR}/bin/libprotocd.dll
        IMPORTED_LOCATION_RELEASE
        ${protobuf_SOURCE_DIR}/bin/libprotoc.dll
        INTERFACE_INCLUDE_DIRECTORIES
        ${protobuf_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDIPROTOBUF::libprotoc>
        DESTINATION release
    )

    add_library(AIDIPROTOBUF::libprotobuf-lite SHARED IMPORTED)
    set_target_properties(
        AIDIPROTOBUF::libprotobuf-lite
        PROPERTIES
        IMPORTED_IMPLIB
        ${protobuf_SOURCE_DIR}/build/Release/x64/libprotobuf-lite.lib
        IMPORTED_IMPLIB_DEBUG
        ${protobuf_SOURCE_DIR}/build/Debug/x64/libprotobuf-lited.lib
        IMPORTED_IMPLIB_RELEASE
        ${protobuf_SOURCE_DIR}/build/Release/x64/libprotobuf-lite.lib
        IMPORTED_LOCATION
        ${protobuf_SOURCE_DIR}/bin/libprotobuf-lite.dll
        IMPORTED_LOCATION_DEBUG
        ${protobuf_SOURCE_DIR}/bin/libprotobuf-lited.dll
        IMPORTED_LOCATION_RELEASE
        ${protobuf_SOURCE_DIR}/bin/libprotobuf-lite.dll
        INTERFACE_INCLUDE_DIRECTORIES
        ${protobuf_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDIPROTOBUF::libprotobuf-lite>
        DESTINATION release
    )
endif()
