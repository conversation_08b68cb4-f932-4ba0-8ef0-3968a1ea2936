﻿include(FetchContent)
set(GFLAGS_DEPS_ROOT "http://pan.aqrose.com/f/7c2091ad67ec46d3bb4f/?dl=1&p=gflags.2.2.2.zip")
FetchContent_Declare(
    gflags
    URL ${GFLAGS_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(gflags)

if(NOT gflags_POPULATED)
    FetchContent_Populate(gflags)
    add_library(AIDIGFLAGS SHARED IMPORTED)
    set_target_properties(
        AIDIGFLAGS
        PROPERTIES
        IMPORTED_IMPLIB
        ${gflags_SOURCE_DIR}/build/Release/x64/gflags.lib
        IMPORTED_IMPLIB_DEBUG
        ${gflags_SOURCE_DIR}/build/Debug/x64/gflagsd.lib
        IMPORTED_IMPLIB_RELEASE
        ${gflags_SOURCE_DIR}/build/Release/x64/gflags.lib
        IMPORTED_LOCATION
        ${gflags_SOURCE_DIR}/bin/gflags.dll
        IMPORTED_LOCATION_DEBUG
        ${gflags_SOURCE_DIR}/bin/gflagsd.dll
        IMPORTED_LOCATION_RELEASE
        ${gflags_SOURCE_DIR}/bin/gflags.dll
        INTERFACE_INCLUDE_DIRECTORIES
        ${gflags_SOURCE_DIR}/include
    )
    install(
        FILES
        $<TARGET_FILE:AIDIGFLAGS>
        DESTINATION release
    )
endif()
