include(FetchContent)
set(PYBIND11_DEPS_ROOT "https://github.com/pybind/pybind11/archive/refs/tags/v2.11.1.zip")

# 设置 pybind11 使用我们找到的 Python
set(PYBIND11_PYTHON_VERSION ${Python3_VERSION})
set(PYTHON_EXECUTABLE ${Python3_EXECUTABLE})

FetchContent_Declare(
    pybind11
    URL ${PYBIND11_DEPS_ROOT}
    UPDATE_DISCONNECTED
)

FetchContent_GetProperties(pybind11)

if(NOT pybind11_POPULATED)
    FetchContent_Populate(pybind11)
    add_subdirectory(${pybind11_SOURCE_DIR} ${pybind11_BINARY_DIR})
endif()


