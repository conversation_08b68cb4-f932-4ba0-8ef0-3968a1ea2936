# AiVideo 插件开发指南

本文档提供了关于如何为 AiVideo 应用程序开发自定义插件的详细指南。AiVideo 支持两种类型的插件：C++ 插件和 Python 脚本插件。

## 目录

- [插件系统概述](#插件系统概述)
- [C++ 插件开发](#c-插件开发)
  - [基本结构](#基本结构)
  - [实现必要的方法](#实现必要的方法)
  - [注册插件](#注册插件)
  - [示例：人流密度分析插件](#示例人流密度分析插件)
- [Python 脚本插件开发](#python-脚本插件开发)
  - [基本结构](#python-脚本基本结构)
  - [必要的函数](#必要的函数)
  - [可选的函数](#可选的函数)
  - [示例：对象计数脚本](#示例对象计数脚本)
  - [示例：事件检测脚本](#示例事件检测脚本)
- [插件配置和参数](#插件配置和参数)
- [处理结果格式](#处理结果格式)
- [调试和测试插件](#调试和测试插件)
- [最佳实践](#最佳实践)

## 插件系统概述

AiVideo 的插件系统允许开发者扩展应用程序的功能，而无需修改核心代码。每个插件都是一个独立的模块，可以处理视频帧、跟踪结果，并生成自定义的分析结果。

插件系统的主要特点：

- **模块化设计**：每个插件都是独立的，可以单独启用或禁用
- **统一接口**：所有插件都实现相同的接口，便于集成
- **结果标准化**：处理结果使用统一的 JSON 格式，便于显示和存储
- **支持多种语言**：可以使用 C++ 或 Python 开发插件
- **批处理支持**：插件可以同时处理多帧图像，提高处理效率
- **帧历史分析**：插件可以访问当前帧及前序帧，实现更复杂的时间序列分析

## C++ 插件开发

### 基本结构

要创建 C++ 插件，需要继承 `ai::plugins::TaskPlugin` 基类并实现其虚函数。基本结构如下：

```cpp
#include "ai/plugins/task_plugin.h"

namespace ai {
namespace plugins {

class MyCustomPlugin : public TaskPlugin {
public:
    // 构造函数
    MyCustomPlugin();

    // 析构函数
    ~MyCustomPlugin() override;

    // 必须实现的方法
    bool initialize() override;
    bool process(cv::Mat& frame, const std::vector<tracking::strack>& tracks, FrameResult& result) override; // 单帧处理接口（已过时）
    bool process_batch(std::vector<cv::Mat>& frames, const std::vector<std::vector<tracking::strack>>& tracks_list, FrameResult& result) override; // 多帧处理接口
    void reset() override;
    int get_required_frames() const override; // 获取需要处理的帧数，默认为5
    std::string get_type() const override;
    std::string get_description() const override;
    std::string get_version() const override;
    std::string get_author() const override;

    // 可选方法
    QIcon get_icon() const override;
    std::vector<QAction*> get_actions() const override;
    QWidget* create_config_widget(QWidget* parent = nullptr) override;
    QString get_config_title() const override;
    void save_config() override;
    void load_config() override;
    QString get_display_name() const override;

private:
    // 插件私有成员变量
};

} // namespace plugins
} // namespace ai
```

### 实现必要的方法

每个 C++ 插件必须实现以下方法：

1. **initialize()**：初始化插件，加载资源，设置初始状态
2. **process()**：处理视频帧和跟踪结果，生成分析结果
3. **reset()**：重置插件状态
4. **get_type()**：返回插件类型
5. **get_description()**：返回插件描述
6. **get_version()**：返回插件版本
7. **get_author()**：返回插件作者

其中最重要的是 `process()` 方法，它负责处理每一帧视频：

```cpp
// 单帧处理接口（已过时）
bool MyCustomPlugin::process(cv::Mat& frame,
                           const std::vector<tracking::strack>& tracks,
                           FrameResult& result) {
    // 调用多帧接口并只使用第一帧结果
    std::vector<cv::Mat> frames = {frame};
    std::vector<std::vector<tracking::strack>> tracks_list = {tracks};
    bool success = process_batch(frames, tracks_list, result);
    if (success && !frames.empty()) {
        frame = frames[0];
    }
    return success;
}

// 多帧处理接口
bool MyCustomPlugin::process_batch(std::vector<cv::Mat>& frames,
                                 const std::vector<std::vector<tracking::strack>>& tracks_list,
                                 FrameResult& result) {
    // 初始化结果
    result.task_type = "my_custom_plugin";

    // 处理每一帧
    for (size_t i = 0; i < frames.size(); ++i) {
        // 确保有对应的跟踪结果
        if (i >= tracks_list.size()) {
            continue;
        }

        cv::Mat& frame = frames[i];
        const auto& tracks = tracks_list[i];

        // 处理视频帧
        // 分析跟踪结果
        // 在帧上绘制可视化效果

        // 如果是当前帧（第一帧），设置处理后的帧作为结果
        if (i == 0) {
            result.processed_frame = frame;
        }
    }

    return true;  // 返回处理是否成功
}
```

### 注册插件

要使插件在应用程序中可用，需要将其注册到 `PluginManager`：

```cpp
// 创建插件实例
auto my_plugin = std::make_shared<MyCustomPlugin>();

// 注册到 AI 处理器
processor->register_plugin(my_plugin);
```

通常，您可以在 `ai/plugins/ui/plugin_registration.cpp` 文件中的 `register_custom_plugins` 函数中注册自定义插件。

### 示例：人流密度分析插件

以下是一个简化的人流密度分析插件示例：

```cpp
#include "ai/plugins/custom_plugin_example.h"
#include <opencv2/opencv.hpp>

namespace ai {
namespace plugins {

CrowdDensityPlugin::CrowdDensityPlugin()
    : TaskPlugin("CrowdDensityPlugin"), density_threshold_(5) {
}

CrowdDensityPlugin::~CrowdDensityPlugin() {
}

bool CrowdDensityPlugin::initialize() {
    // 初始化插件
    return true;
}

bool CrowdDensityPlugin::process(cv::Mat& frame,
                               const std::vector<tracking::strack>& tracks,
                               FrameResult& result) {
    // 计算当前帧中的人数
    int person_count = 0;
    for (const auto& track : tracks) {
        if (track.detect_class == "person") {
            person_count++;
        }
    }

    // 判断是否超过密度阈值
    bool is_crowded = person_count > density_threshold_;

    // 在帧上绘制信息
    std::string status = is_crowded ? "拥挤" : "正常";
    cv::putText(frame, "人流状态: " + status, cv::Point(10, 30),
                cv::FONT_HERSHEY_SIMPLEX, 0.7, is_crowded ? cv::Scalar(0, 0, 255) : cv::Scalar(0, 255, 0), 2);
    cv::putText(frame, "人数: " + std::to_string(person_count), cv::Point(10, 60),
                cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 0, 255), 2);

    // 填充结果
    result.task_type = "crowd_density";
    result.class_counts["person"] = person_count;
    result.total_count = person_count;
    result.ext_info["is_crowded"] = is_crowded;

    return true;
}

void CrowdDensityPlugin::reset() {
    // 重置插件状态
}

std::string CrowdDensityPlugin::get_type() const {
    return "crowd_density";
}

std::string CrowdDensityPlugin::get_description() const {
    return "分析视频中的人流密度，检测拥挤情况";
}

std::string CrowdDensityPlugin::get_version() const {
    return "1.0.0";
}

std::string CrowdDensityPlugin::get_author() const {
    return "阿丘科技";
}

int CrowdDensityPlugin::get_required_frames() const {
    // 返回需要处理的帧数，这里只需要当前帧
    return 1;
}

void CrowdDensityPlugin::set_density_threshold(int threshold) {
    density_threshold_ = threshold;
}

int CrowdDensityPlugin::get_density_threshold() const {
    return density_threshold_;
}

} // namespace plugins
} // namespace ai
```

## Python 脚本开发

AiVideo 支持两种类型的 Python 脚本：

1. **任务插件脚本**（位于 `scripts/task_plugins/` 目录）：用于实现自定义处理逻辑，如计数、事件检测等。这些脚本必须实现 `process` 函数来处理视频帧和跟踪结果。

2. **帧处理脚本**（位于 `scripts/frame_processors/` 目录）：用于在模型推理前处理多帧图像，如图像增强、背景减除、光流计算等。这些脚本必须实现 `process_frames` 函数来处理多帧图像。

### Python 脚本基本结构

Python 脚本插件的基本结构如下：

```python
# -*- coding: utf-8 -*-
"""
插件描述
"""

import numpy as np
import cv2

# 全局变量
params = {}
frame_count = 0

def initialize(parameters):
    """初始化函数，在插件加载时调用"""
    global params, frame_count
    params = parameters
    frame_count = 0
    print("插件初始化，参数:", params)

def process(frames, tracks_list):
    """处理函数，处理多帧图像和对应的跟踪结果"""
    global frame_count

    # 创建结果字典
    result = {
        "task_type": "my_python_plugin",
        "frame_id": frame_count,
        "total_count": 0,
        "class_counts": {},
        "ext_info": {
            "frames_received": len(frames),
            "tracks_received": len(tracks_list)
        }
    }

    # 处理每一帧
    for frame_idx, (frame, tracks) in enumerate(zip(frames, tracks_list)):
        # 只在当前帧（第一帧）更新计数
        if frame_idx == 0:
            frame_count += 1
            # 当前帧的处理结果将作为最终返回结果
            result["processed_frame"] = frame

        # 处理逻辑
        # 如果需要使用历史帧进行分析，可以访问 frames 和 tracks_list 中的其他元素
        # 例如，如果需要比较当前帧和前一帧：
        # current_frame = frames[0]
        # previous_frame = frames[1] if len(frames) > 1 else None

    return result

def reset():
    """重置函数，在插件重置时调用"""
    global frame_count
    frame_count = 0
    print("插件重置")

def get_info():
    """获取插件信息"""
    return {
        "type": "my_python_plugin",
        "name": "我的Python插件",
        "description": "这是一个示例Python插件",
        "version": "1.0.0",
        "author": "开发者姓名"
    }
```

### 必要的函数

#### 任务插件脚本（scripts/task_plugins/）

任务插件脚本必须实现以下函数：

1. **process(frames, tracks_list)**：处理多帧视频和对应的跟踪结果，返回单个处理结果或包含单个结果的列表（为了兼容旧版本）。注意：C++端只会使用第一个结果。

#### 帧处理脚本（scripts/frame_processors/）

帧处理脚本必须实现以下函数：

1. **process_frames(frames)**：处理多帧图像，返回一个处理后的图像

注意：接口传递的是帧列表，而不是单个帧。列表中的第一个元素是当前帧，后续元素是历史帧。

### 可选的函数

以下函数对于任务插件和帧处理脚本都是可选的，但建议实现：

1. **initialize(parameters)**：初始化插件，接收配置参数
2. **reset()**：重置插件状态
3. **get_info()**：返回插件信息
4. **get_required_frames()**：返回插件需要处理的帧数（任务插件默认为5，帧处理脚本默认为5）

### 示例：对象计数脚本

以下是一个对象计数脚本的示例：

```python
# -*- coding: utf-8 -*-
"""
对象计数脚本
"""

import numpy as np
import cv2

# 全局变量
params = {}
frame_count = 0
total_count = 0
class_counts = {}
track_history = {}  # 跟踪历史，用于记录已计数的轨迹

def initialize(parameters):
    """初始化函数"""
    global params, frame_count, total_count, class_counts, track_history
    params = parameters
    frame_count = 0
    total_count = 0
    class_counts = {}
    track_history = {}
    print("对象计数脚本初始化，参数:", params)

def process(frames, tracks_list):
    """处理函数，处理多帧图像和对应的跟踪结果

    Args:
        frames: 帧列表，第一个元素是当前帧，后续元素是历史帧
        tracks_list: 跟踪结果列表，每个元素对应一帧的跟踪结果

    Returns:
        包含处理结果的字典
    """
    global frame_count, total_count, class_counts, track_history

    # 创建结果字典
    result = {
        "task_type": "object_counter",
        "frame_id": frame_count,
        "total_count": total_count,
        "class_counts": class_counts,
        "ext_info": {
            "frame_count": frame_count,
            "frames_received": len(frames),
            "tracks_received": len(tracks_list)
        }
    }

    # 处理每一帧
    for frame_idx, (frame, tracks) in enumerate(zip(frames, tracks_list)):
        # 只在当前帧（第一帧）更新计数
        if frame_idx == 0:
            frame_count += 1

            # 获取计数线参数
            count_line_y = int(params.get("count_line_y", str(frame.shape[0] // 2)))

            # 绘制计数线
            cv2.line(frame, (0, count_line_y), (frame.shape[1], count_line_y), (0, 255, 255), 2)

            # 处理跟踪结果
            for track in tracks:
                # 获取跟踪信息
                track_id = track["track_id"]
                detect_class = track["detect_class"]
                tlwh = track["tlwh"]

                # 计算目标中心点
                center_x = int(tlwh["x"] + tlwh["width"] / 2)
                center_y = int(tlwh["y"] + tlwh["height"] / 2)

                # 更新跟踪历史
                if track_id not in track_history:
                    track_history[track_id] = {
                        "class": detect_class,
                        "last_y": center_y,
                        "counted": False
                    }
                else:
                    last_y = track_history[track_id]["last_y"]

                    # 检查是否穿过计数线
                    if not track_history[track_id]["counted"] and ((last_y < count_line_y and center_y >= count_line_y) or
                                                                 (last_y > count_line_y and center_y <= count_line_y)):
                        # 更新计数
                        if detect_class not in class_counts:
                            class_counts[detect_class] = 0
                        class_counts[detect_class] += 1
                        total_count += 1

                        # 标记为已计数
                        track_history[track_id]["counted"] = True

                    # 更新最后位置
                    track_history[track_id]["last_y"] = center_y

                # 在图像上绘制跟踪框
                x, y = int(tlwh["x"]), int(tlwh["y"])
                w, h = int(tlwh["width"]), int(tlwh["height"])
                color = (0, 255, 0) if track_history[track_id]["counted"] else (255, 0, 0)
                cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)

            # 绘制统计信息
            cv2.putText(frame, f"Total: {total_count}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

            # 返回处理后的帧
            result["processed_frame"] = frame

    # 更新结果中的计数信息
    result["total_count"] = total_count
    result["class_counts"] = class_counts

    return result

def reset():
    """重置函数"""
    global frame_count, total_count, class_counts, track_history
    frame_count = 0
    total_count = 0
    class_counts = {}
    track_history = {}
    print("对象计数脚本重置")

def get_info():
    """获取插件信息"""
    return {
        "type": "object_counter",
        "name": "对象计数",
        "description": "对象计数脚本，统计穿过计数线的对象数量",
        "version": "1.0.0",
        "author": "阿丘科技"
    }
```

### 示例：指定帧数的脚本

以下是一个指定需要帧数的脚本示例：

```python
# -*- coding: utf-8 -*-
"""
指定帧数示例插件
"""

import numpy as np
import cv2

# 全局变量
required_frames = 10  # 需要 10 帧

def initialize(parameters):
    """初始化函数"""
    global required_frames

    # 从参数中读取需要的帧数（如果有）
    if 'required_frames' in parameters:
        try:
            required_frames = int(parameters['required_frames'])
            # 确保帧数在合理范围内
            if required_frames < 1:
                required_frames = 1
            elif required_frames > 100:
                required_frames = 100
        except:
            required_frames = 10  # 如果转换失败，使用默认值

    print(f"插件初始化，需要帧数: {required_frames}")

def get_required_frames():
    """返回插件需要处理的帧数"""
    global required_frames
    return required_frames

def process(frames, tracks_list):
    """处理函数，处理多帧图像和对应的跟踪结果"""
    # 创建结果列表
    results = []

    # 打印收到的帧数
    print(f"收到 {len(frames)} 帧进行处理")

    # 处理每一帧
    for frame_idx, (frame, tracks) in enumerate(zip(frames, tracks_list)):
        # 在图像上绘制信息
        img = frame.copy()
        cv2.putText(img, f"Frame {frame_idx}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        # 创建当前帧的结果字典
        result = {
            "task_type": "frame_count_example",
            "frame_id": frame_idx,
            "ext_info": {
                "batch_index": frame_idx,
                "frames_received": len(frames),
                "frames_requested": required_frames
            },
            "processed_frame": img  # 返回处理后的帧
        }

        # 添加到结果列表
        results.append(result)

    return results

def reset():
    """重置函数"""
    print("插件已重置")

def get_info():
    """获取插件信息"""
    return {
        "type": "frame_count_example",
        "name": "帧数示例插件",
        "description": "演示如何指定需要的帧数的示例插件",
        "version": "1.0.0",
        "author": "阿丘科技"
    }
```

### 示例：事件检测脚本

以下是一个事件检测脚本的示例：

```python
# -*- coding: utf-8 -*-
"""
事件检测脚本
"""

import numpy as np
import cv2
import time

# 全局变量
params = {}
frame_count = 0
events = []
lingering_threshold = 50  # 逗留阈值（帧数）
track_history = {}  # 跟踪历史

def initialize(parameters):
    """初始化函数"""
    global params, frame_count, events, track_history, lingering_threshold
    params = parameters
    frame_count = 0
    events = []
    track_history = {}

    # 从参数中获取逗留阈值
    if "lingering_threshold" in params:
        try:
            lingering_threshold = int(params["lingering_threshold"])
        except ValueError:
            pass

    print("事件检测脚本初始化，参数:", params)

def process(frame, tracks):
    """处理函数"""
    global frame_count, events, track_history

    frame_count += 1
    result = {}

    # 处理跟踪目标
    for track in tracks:
        track_id = track["track_id"]
        tlwh = track["tlwh"]
        bbox = [tlwh["x"], tlwh["y"], tlwh["width"], tlwh["height"]]
        class_name = track["detect_class"]

        # 更新跟踪历史
        if track_id not in track_history:
            track_history[track_id] = {
                'first_seen': frame_count,
                'positions': [],
                'class_name': class_name
            }

        track_history[track_id]['positions'].append((bbox[0], bbox[1]))

        # 检查逗留行为
        if len(track_history[track_id]['positions']) > lingering_threshold:
            # 计算移动距离
            first_pos = track_history[track_id]['positions'][0]
            current_pos = track_history[track_id]['positions'][-1]
            distance = np.sqrt((current_pos[0] - first_pos[0])**2 +
                             (current_pos[1] - first_pos[1])**2)

            # 如果移动距离小于阈值，认为是逗留行为
            if distance < 100:  # 像素距离阈值
                # 创建事件
                event = {
                    'type': 2,  # LINGERING事件类型
                    'track_id': track_id,
                    'object_class': class_name,
                    'duration': frame_count - track_history[track_id]['first_seen'],
                    'description': f"检测到{class_name}在区域逗留",
                    'timestamp': int(time.time() * 1000),
                    'is_active': True,
                    'location': {
                        'x': int(bbox[0]),
                        'y': int(bbox[1]),
                        'width': int(bbox[2]),
                        'height': int(bbox[3])
                    }
                }
                events.append(event)

    # 返回处理结果
    result["processed_frame"] = frame
    result["events"] = events
    result["active_events"] = events
    result["task_type"] = "event_detection"
    result["frame_id"] = frame_count

    return result

def reset():
    """重置函数"""
    global frame_count, events, track_history
    frame_count = 0
    events = []
    track_history = {}
    print("事件检测脚本重置")

def get_info():
    """获取脚本信息"""
    return {
        "type": "event_detection",
        "name": "事件检测",
        "description": "检测视频中的异常事件，如人员逗留等",
        "version": "1.0.0",
        "author": "阿丘科技"
    }
```

## 插件配置和参数

插件可以通过参数进行配置。对于 C++ 插件，可以实现 `create_config_widget()` 方法来创建配置界面。对于 Python 脚本插件，可以通过 `initialize(parameters)` 函数接收参数。

### C++ 插件配置示例

```cpp
QWidget* MyCustomPlugin::create_config_widget(QWidget* parent) {
    QWidget* widget = new QWidget(parent);
    QVBoxLayout* layout = new QVBoxLayout(widget);

    // 创建配置控件
    QSpinBox* thresholdSpinBox = new QSpinBox(widget);
    thresholdSpinBox->setRange(1, 100);
    thresholdSpinBox->setValue(threshold_);

    // 添加到布局
    QFormLayout* formLayout = new QFormLayout();
    formLayout->addRow("阈值:", thresholdSpinBox);
    layout->addLayout(formLayout);

    // 连接信号
    QObject::connect(thresholdSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                    [this](int value) { threshold_ = value; });

    return widget;
}
```

### Python 脚本参数示例

```python
def initialize(parameters):
    global params
    params = parameters

    # 获取参数值
    threshold = int(params.get("threshold", "10"))
    print(f"使用阈值: {threshold}")
```

## 处理结果格式

插件的处理结果应该遵循统一的格式，以便应用程序可以正确显示和存储结果。结果通常包含以下字段：

- **task_type**：任务类型，用于标识插件
- **frame_id**：当前帧的 ID
- **total_count**：总计数（如果适用）
- **class_counts**：各类别的计数（如果适用）
- **events**：检测到的事件（如果适用）
- **ext_info**：自定义数据，可以包含任何插件特定的信息

对于 C++ 插件，结果通过 `FrameResult` 结构体传递：

```cpp
// 初始化结果列表
results.clear();
results.resize(frames.size());

// 处理每一帧
for (size_t i = 0; i < frames.size(); ++i) {
    // 初始化当前帧的结果
    FrameResult& result = results[i];

    // 填充结果
    result.task_type = "my_plugin";
    result.frame_id = (i == 0) ? frame_id : frame_id - i;
    result.total_count = total_count;
    result.class_counts["person"] = person_count;
    result.ext_info["my_value"] = 123;
    result.ext_info["batch_index"] = static_cast<int>(i);
    result.ext_info["is_current_frame"] = (i == 0);

    // 设置处理后的帧
    result.processed_frame = frames[i];
}
```

对于 Python 脚本插件，结果通过返回字典列表传递：

```python
results = []
for frame_idx, (frame, tracks) in enumerate(zip(frames, tracks_list)):
    result = {
        "task_type": "my_python_plugin",
        "frame_id": frame_count if frame_idx == 0 else frame_count - frame_idx,
        "total_count": total_count,
        "class_counts": {"person": person_count},
        "ext_info": {
            "my_value": 123,
            "batch_index": frame_idx,
            "is_current_frame": frame_idx == 0
        },
        "processed_frame": frame  # 可选，返回处理后的帧
    }
    results.append(result)
return results
```

## 调试和测试插件

### C++ 插件调试

1. 在 IDE 中设置断点
2. 使用日志输出调试信息：`std::cout << "Debug info: " << value << std::endl;`
3. 在处理函数中绘制调试信息：`cv::putText(frame, "Debug: " + std::to_string(value), cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 0, 255), 2);`

### Python 脚本调试

1. 使用 `print()` 函数输出调试信息
2. 在处理函数中绘制调试信息：`cv2.putText(frame, f"Debug: {value}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)`
3. 使用 `try-except` 块捕获和打印异常：

```python
try:
    # 可能出错的代码
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
```

## 帧历史功能

AiVideo 插件系统支持帧历史功能，允许插件同时处理当前帧和前序帧。这个功能对于需要时间序列分析的任务非常有用，例如运动检测、行为分析等。

### 指定需要的帧数

插件可以通过实现 `get_required_frames()` 方法来指定需要处理的帧数。默认情况下，插件会收到 5 帧（当前帧加上 4 个历史帧）。

#### C++ 插件指定帧数

```cpp
// 在插件类中重写这个方法
// 返回值必须大于等于 1，小于等于 100
// 如果返回值超出范围，将被限制在这个范围内
int MyCustomPlugin::get_required_frames() const override {
    return 10;  // 需要 10 帧（当前帧加上 9 个历史帧）
}
```

#### Python 插件指定帧数

```python
def get_required_frames():
    """返回插件需要处理的帧数"""
    return 10  # 需要 10 帧（当前帧加上 9 个历史帧）
```

注意：
- 返回值必须是一个整数
- 返回值必须大于等于 1（至少需要当前帧）
- 系统会限制最大帧数为 100，如果返回值大于 100，将被截断为 100
- 如果历史帧数不足，系统会提供尽可能多的帧，但不会超过请求的数量

### 帧历史的工作原理

1. **帧缓存**：系统会自动维护最近处理的帧和对应的跟踪结果，最多保存用户设置的历史帧数量。

2. **批量处理**：当调用插件的 `process_batch` 方法时，系统会传递当前帧及其历史帧，允许插件同时处理多帧。

3. **结果处理**：插件需要返回每一帧的处理结果，但只有当前帧（第一帧）的结果会被显示和存储。

### 在 Python 脚本中使用帧历史

```python
def process(frames, tracks_list):
    # frames 是帧列表，第一个元素是当前帧，后续元素是历史帧
    current_frame = frames[0]

    # 如果有历史帧，可以访问
    if len(frames) > 1:
        previous_frame = frames[1]  # 前一帧

    # 同样可以访问对应的跟踪结果
    current_tracks = tracks_list[0]
    if len(tracks_list) > 1:
        previous_tracks = tracks_list[1]  # 前一帧的跟踪结果

    # 例如，计算目标的移动速度
    for current_track in current_tracks:
        track_id = current_track["track_id"]

        # 在前一帧中查找相同 ID 的跟踪目标
        if len(tracks_list) > 1:
            for prev_track in tracks_list[1]:
                if prev_track["track_id"] == track_id:
                    # 计算位置变化
                    dx = current_track["tlwh"]["x"] - prev_track["tlwh"]["x"]
                    dy = current_track["tlwh"]["y"] - prev_track["tlwh"]["y"]
                    # 计算速度或其他特征
                    speed = (dx**2 + dy**2)**0.5
                    break
```

## 最佳实践

1. **性能优化**：插件会在每一帧上执行，因此性能至关重要。避免在每帧中执行耗时操作。
2. **错误处理**：妥善处理异常和错误情况，避免插件崩溃导致整个应用程序崩溃。
3. **资源管理**：在初始化时分配资源，在析构函数或重置函数中释放资源。
4. **参数验证**：验证输入参数，确保它们在有效范围内。
5. **文档**：为插件提供详细的文档，包括功能描述、参数说明和使用示例。
6. **版本控制**：为插件设置版本号，便于跟踪更新和兼容性。
7. **模块化设计**：将复杂功能分解为小型、可管理的组件。
8. **代码风格**：遵循项目的代码风格指南，保持代码一致性。
9. **批处理优化**：利用多帧处理接口进行批处理优化，提高处理效率。

---

通过遵循本指南，您可以为 AiVideo 应用程序开发功能强大、高效的插件，扩展其视频分析能力。如有任何问题或需要进一步的帮助，请联系开发团队。
